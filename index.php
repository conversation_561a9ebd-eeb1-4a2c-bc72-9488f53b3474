<?php include("components/header.php"); ?>
<head>
    <title>Button Page</title>
    <link href="public/css/tailwindLocal.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            background: linear-gradient(135deg, #f0f0f0 25%, #d0d0d0 100%);
            margin: 0;
            padding: 0;
        }
        h1 {
            color: #990000;
            font-family: 'Trebuchet MS', sans-serif;
            text-align: center;
            font-size: 30px;
            font-weight: bold;
            padding: 20px;
            background: #fff;
            margin: 0;
            border-bottom: 2px solid #0366d6;
        }
        p.intro-text {
            color: #0000e6;
            font-family: 'Brush Script MT', cursive;
            font-size: 16px;
            width: 80%;
            margin: 20px auto;
            text-align: center;
        }
        .carousel {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        }
        .carousel-inner {
            position: relative;
            overflow: hidden;
            height: 500px;
            width: 100%;
        }
        .carousel-item img {
            height: 500px;
            width: 100%;
            object-fit: cover;
        }
        .carousel-item {
            display: none;
        }
        .carousel-item.active {
            display: block;
        }
        .carousel-controls {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            display: flex;
            justify-content: space-between;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .carousel:hover .carousel-controls {
            opacity: 1;
        }
        .carousel-control {
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
        }
        .button-container {
            text-align: center;
            margin-top: 20px;
        }
        .button-container a, .button-container button {
            background-color: #0366d6;
            color: white;
            font-weight: bold;
            padding: 14px 32px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            transition: background-color 0.3s ease;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }
        .button-container a:hover, .button-container button:hover {
            background-color: #0056b3;
        }
        .generate-report-container {
            margin-top: 20px;
            text-align: center;
        }
        .generate-report-container form {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            max-width: 600px;
            margin: 0 auto;
        }
        .generate-report-container label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .generate-report-container input {
            width: calc(100% - 20px);
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .generate-report-container button {
            padding: 14px 32px;
            font-size: 16px;
            color: white;
            background-color: #0366d6;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }
        .generate-report-container button:hover {
            background-color: #0056b3;
        }
        .footer {
            background-color: #03263E;
            color: white;
            padding: 10px 0;
            text-align: center;
            position: fixed;
            width: 100%;
            bottom: 0;
        }
        .footer a {
            color: #0366d6;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h1>WELCOME TO SPIC TAPE DATA MANAGEMENT SYSTEM</h1>
    <p class="intro-text">
        <i>"SeisData Processing and Interpretation Centre at NBP Green Height, Mumbai is one of the Seismic Data Processing centres of ONGC. The Centre processes huge volume of Seismic data using state-of-the-art Enterprise Software solutions. The acquired Seismic Data are received from the Operational fields mostly the Western Offshore Basin through various Tape Cartridges(3590/3592/LTO etc) and these Cartridges are managed at SPIC, TDMS. This portal has been developed to ensure secure and efficient Tape management using TS3500(E06,E07 drives),TS4500(E08,60f drives) and LTO drives as well as to accurately record and track tape shipments to various Centres and Locations."</i>
    </p>

    <div class="carousel relative mt-4">
        <div class="carousel-inner relative overflow-hidden w-full">
            <div class="carousel-item active">
                <img src="public/images/img1.png" alt="Slide 1">
            </div>
            <div class="carousel-item">
                <img src="public/images/img2.png" alt="Slide 2">
            </div>
            <div class="carousel-item">
                <img src="public/images/img3.png" alt="Slide 3">
            </div>
        </div>
        <div class="carousel-controls">
            <button class="carousel-control" onclick="prevSlide()">&#9664;</button>
            <button class="carousel-control" onclick="nextSlide()">&#9654;</button>
        </div>
    </div>

    <div class="button-container mt-8 flex justify-center space-x-4">
        <a href="auth_login.php" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">Raise a Request</a>
        <a href="tape.php" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">View Data</a>
        <button class="bg-blue-500 text-white font-semibold py-2 px-4 rounded" onclick="generateReport()">Generate Report</button>
    </div>

    <div class="generate-report-container mt-8 hidden" id="generateReportSection">
        <form action="generate_report.php" method="POST" class="bg-white p-4 rounded shadow-md max-w-md mx-auto">
            <div class="mb-4">
                <label for="startDate" class="block text-sm font-medium text-gray-700">Start Date:</label>
                <input type="date" id="startDate" name="startDate" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            </div>
            <div class="mb-4">
                <label for="endDate" class="block text-sm font-medium text-gray-700">End Date:</label>
                <input type="date" id="endDate" name="endDate" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            </div>
            <button type="submit" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">Submit</button>
        </form>
    </div>

    

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.carousel-item');

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(currentSlide);
        }

        // Auto slide change every 10 seconds
        setInterval(nextSlide, 10000);

        function generateReport() {
            window.location.href = "genreport.php";
        }
    </script>
</body>
</html>

