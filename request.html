<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0; /* Light gray background */
            background: linear-gradient(135deg, #f0f0f0 25%, #d0d0d0 100%);
        }
        .header {
            background-color: #03263E;
            color: #FFFFFF;
            padding: 20px;
            text-align: left;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .logo {
            width: 150px;
            margin-right: 20px;
        }
        .header h1 {
            font-size: 28px;
            margin: 0;
            color: #FFFFFF;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        .container {
            padding: 40px;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            font-size: 36px;
            color: #03263E;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        form {
            max-width: 500px;
            margin: 0 auto;
            background-color: #FFFFFF;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            text-align: left;
        }
        input, select {
            width: calc(100% - 20px);
            padding: 10px; /* Reduced padding */
            font-size: 10px; /* Reduced font size */
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            margin-bottom: 15px; /* Adjusted margin */
            transition: border-color 0.3s ease;
        }
        input:focus, select:focus {
            border-color: #0366d6;
        }
        button {
            padding: 14px 32px;
            font-size: 16px;
            color: white;
            background-color: #0366d6;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 20px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .footer {
            margin-top: 40px;
            font-size: 14px;
            color: #7f8c8d;
            text-align: center;
        }
        .footer a {
            color: #0366d6;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .back-button {
            display: inline-block;
            margin-bottom: 10px;
            padding: 10px 20px;
            font-size: 16px;
            color: white;
            background-color: #007BFF;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        .back-button:hover {
            background-color: #0056b3;
        }

    </style>
</head>
<body>
    <header class="header">
        <img src="ongc_logo.png" alt="Logo" class="logo">
        
    </header>
    <a href='index.html' class='back-button'>Back</a>
    <div class="container">
        <h1>Seisdata Processing Center</h1>
        <marquee behavior="scroll" direction="left">Welcome to Tape Library</marquee>
    <div class="container">
        
        <form method="post" action="submit-request.php">
            <label for="cpf-number">CPF Number</label>
            <input type="text" id="cpf_number" name="cpf_number" required>
           <label for="date">Name</label>
            <input type="text" id="username" name="username" required>
            <label for="tapeid">Tape ID</label>
            <input type="text" id="tapeid" name="tapeid" required>
            <label for="datatype">Datatype</label>
            <input type="text" id="datatype" name="datatype" required>
            <label for="date">Date</label>
            <input type="date" id="date" name="date" required>

            <label for="area">Area</label>
            <input type="text" id="area" name="area" required>

            <label for="survey-name">Survey Name</label>
            <input type="text" id="survey_name" name="survey_name" required>

            <label for="project-name">Project Name</label>
            <input type="text" id="project_name" name="project_name" required>

            <label for="acquisition-year">Acquisition Year</label>
            <input type="number" id="acquisition_year" name="acquisition_year" required>

            <label for="group">Group_Number</label>
            <select id="group" name="group" required>
                <option value="Group 1">Group 1</option>
                <option value="Group 2">Group 2</option>
                <option value="Group 3">Group 3</option>
                <option value="Group 4">Group 4</option>
            </select>
            <label for="activity">Activity</label>
            <select id="activity" name="activity" required>
                <option value="Sent to DGH">Sent to DGH</option>
                <option value="Received from TDMS">Received from TDMS</option>
            </select>

            <label for="remarks">Remarks</label>
            <input type="text" id="remarks" name="remarks">
            <button type="submit">Submit Request</button>
        </form>
    </div>
    <div class="footer">
        <p>For more information, visit our <a href="#">homepage</a> or <a href="#">contact us</a>.</p>
    </div>
</body>
</html>
