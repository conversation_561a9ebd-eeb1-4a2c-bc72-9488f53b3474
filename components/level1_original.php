<body class="bg-gray-100">
    <?php
    if (isset($_GET['message'])) {
        echo '<div class="bg-green-500 text-white p-4 rounded mb-4">' . htmlspecialchars($_GET['message']) . '</div>';
    }
    ?>
    <div class="container mx-auto p-4">
        <h1 class="text-3xl font-bold text-center mb-4">TAPE LIBRARY</h1>
        <marquee behavior="scroll" direction="left" class="text-lg text-gray-700">Welcome to Tape Library</marquee>
        
        <div class="overflow-x-auto">
            <table class='table-auto w-full bg-white shadow-md rounded-lg border border-black'>
                <thead>
                    <tr class="bg-gray-200 text-left text-sm uppercase border-b border-black">
                        <th class="py-2 px-4 border-r border-black">ID</th>
                        <th class="py-2 px-4 border-r border-black">CPF-Number</th>
                        <th class="py-2 px-4 border-r border-black">Name</th>
                        <th class="py-2 px-4 border-r border-black">Tape-ID</th>
                        <th class="py-2 px-4 border-r border-black">Datatype</th>
                        <th class="py-2 px-4 border-r border-black">Date</th>
                        <th class="py-2 px-4 border-r border-black">Area</th>
                        <th class="py-2 px-4 border-r border-black">Survey Name</th>
                        <th class="py-2 px-4 border-r border-black">Project Name</th>
                        <th class="py-2 px-4 border-r border-black">Acquisition Year</th>
                        <th class="py-2 px-4 border-r border-black">Group</th>
                        <th class="py-2 px-4 border-r border-black">Activity</th>
                        <th class="py-2 px-4 border-black">Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    include("database.php");
                     $startDate = $_POST['startDate'];
                     $endDate = $_POST['endDate'];
                // Fetch data from MySQL table
               /* $sql = "SELECT id,cpf_number,username,tapeid,datatype, date, area, survey_name, project_name, acquisition_year, group_name,activity,remarks FROM tapedetails WHERE date BETWEEN '$startDate' AND '$endDate'";*/

                    $sql = "SELECT id, cpf_number, username, tapeid, datatype, date, area, survey_name, project_name, acquisition_year, group_name, activity, remarks FROM tapedetails";
                    $result = $conn->query($sql);

                    if ($result->num_rows > 0) {
                        while($row = $result->fetch_assoc()) {
                            echo "<tr class='hover:bg-gray-100'>";
                            echo "<td class='py-2 px-4 border-r border-b border-black'>" . $row["id"] . "</td>";
                            echo "<td class='py-2 px-4 border-r border-b border-black'>" . $row["cpf_number"] . "</td>";
                            echo "<td class='py-2 px-4 border-r border-b border-black'>" . $row["username"] . "</td>";
                            echo "<td class='py-2 px-4 border-r border-b border-black'>" . $row["tapeid"] . "</td>";
                            echo "<td class='py-2 px-4 border-r border-b border-black'>" . $row["datatype"] . "</td>";
                            echo "<td class='py-2 px-4 border-r border-b border-black'>" . $row["date"] . "</td>";
                            echo "<td class='py-2 px-4 border-r border-b border-black'>" . $row["area"] . "</td>";
                            echo "<td class='py-2 px-4 border-r border-b border-black'>" . $row["survey_name"] . "</td>";
                            echo "<td class='py-2 px-4 border-r border-b border-black'>" . $row["project_name"] . "</td>";
                            echo "<td class='py-2 px-4 border-r border-b border-black'>" . $row["acquisition_year"] . "</td>";
                            echo "<td class='py-2 px-4 border-r border-b border-black'>" . $row["group_name"] . "</td>";
                            echo "<td class='py-2 px-4 border-r border-b border-black'>" . $row["activity"] . "</td>";
                            echo "<td class='py-2 px-4 border-black border-b'>" . $row["remarks"] . "</td>";
                            echo "</tr>";
                        }
                    } else {
                        echo "<tr><td colspan='13' class='py-2 px-4 text-center border border-black'>No results</td></tr>";
                    }

                    $conn->close();
                    ?>
                </tbody>
            </table>

        </div>
    </div>
    <div class="flex justify-center my-4">
        <a href="index.php"><button type="button" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">Go Back</button></a>&nbsp;&nbsp;
        <a href="request.php"><button type="button" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">Raise a Request</button></a>
    </div>
    <!-- <script type="text/javascript">
        $(document).ready(function() {
            $('#results-table').DataTable();
        });
    </script> -->
</body>
<br>
