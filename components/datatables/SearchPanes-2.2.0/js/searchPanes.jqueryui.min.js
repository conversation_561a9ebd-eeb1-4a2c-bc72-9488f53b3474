/*! Bootstrap integration for DataTables' SearchPanes
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var a,o;"function"==typeof define&&define.amd?define(["jquery","datatables.net-jqui","datatables.net-searchpanes"],function(e){return n(e,window,document)}):"object"==typeof exports?(a=require("jquery"),o=function(e,t){t.fn.dataTable||require("datatables.net-jqui")(e,t),t.fn.dataTable.SearchPanes||require("datatables.net-searchpanes")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||a(e),o(e,t),n(t,0,e.document)}:(o(window,a),module.exports=n(a,window,window.document))):n(jQuery,window,document)}(function(e,t,n,a){"use strict";var o=e.fn.dataTable;return e.extend(!0,o.SearchPane.classes,{disabledButton:"dtsp-paneInputButton dtsp-disabledButton",paneButton:"dtsp-paneButton ui-button",topRow:"dtsp-topRow ui-state-default"}),e.extend(!0,o.SearchPanes.classes,{clearAll:"dtsp-clearAll ui-button",collapseAll:"dtsp-collapseAll ui-button",container:"dtsp-searchPanes",panes:"dtsp-panesContainer fg-toolbar ui-toolbar ui-widget-header",showAll:"dtsp-showAll ui-button"}),o});