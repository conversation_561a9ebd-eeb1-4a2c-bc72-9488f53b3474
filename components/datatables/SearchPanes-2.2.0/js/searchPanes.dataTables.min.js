/*! Bootstrap integration for DataTables' SearchPanes
 * © SpryMedia Ltd - datatables.net/license
 */
!function(t){var a,d;"function"==typeof define&&define.amd?define(["jquery","datatables.net-dt","datatables.net-searchpanes"],function(e){return t(e,window,document)}):"object"==typeof exports?(a=require("jquery"),d=function(e,n){n.fn.dataTable||require("datatables.net-dt")(e,n),n.fn.dataTable.SearchPanes||require("datatables.net-searchpanes")(e,n)},"undefined"==typeof window?module.exports=function(e,n){return e=e||window,n=n||a(e),d(e,n),t(n,0,e.document)}:(d(window,a),module.exports=t(a,window,window.document))):t(jQuery,window,document)}(function(e,n,t,a){"use strict";return e.fn.dataTable});