/*! semantic ui integration for DataTables' SearchPanes
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var a,o;"function"==typeof define&&define.amd?define(["jquery","datatables.net-se","datatables.net-searchpanes"],function(e){return n(e,window,document)}):"object"==typeof exports?(a=require("jquery"),o=function(e,t){t.fn.dataTable||require("datatables.net-se")(e,t),t.fn.dataTable.SearchPanes||require("datatables.net-searchpanes")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||a(e),o(e,t),n(t,0,e.document)}:(o(window,a),module.exports=n(a,window,window.document))):n(jQuery,window,document)}(function(e,t,n,a){"use strict";var o=e.fn.dataTable;return e.extend(!0,o.SearchPane.classes,{buttonGroup:"right floated ui buttons column",disabledButton:"disabled",narrowSearch:"dtsp-narrowSearch",narrowSub:"dtsp-narrow",paneButton:"basic ui",paneInputButton:"circular search link icon",topRow:"row dtsp-topRow"}),e.extend(!0,o.SearchPanes.classes,{clearAll:"dtsp-clearAll basic ui button",collapseAll:"dtsp-collapseAll basic ui button",disabledButton:"disabled",showAll:"dtsp-showAll basic ui button"}),o.SearchPane.prototype._searchContSetup=function(){e('<i class="'+this.classes.paneInputButton+'"></i>').appendTo(this.dom.searchCont)},o});