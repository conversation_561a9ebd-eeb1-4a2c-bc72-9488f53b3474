
@mixin active-tick {
	position: absolute;
	top: 50%;
	margin-top: -10px;
	right: 1em;
	display: inline-block;
	content: '\2713'; // utf8 tick
	color: inherit;
}

div.dataTables_wrapper {
	position: relative;
}

div.dt-buttons {
	position: initial;

	.dt-button {
		overflow: hidden;
		text-overflow: ellipsis;
	}
}

div.dt-button-info {
	position: fixed;
	top: 50%;
	left: 50%;
	width: 400px;
	margin-top: -100px;
	margin-left: -200px;
	background-color: white;
	border-radius: 0.75em;
	box-shadow: 3px 4px 10px 1px rgba(0, 0, 0, 0.8);
	text-align: center;
	z-index: 2003;
	overflow: hidden;

	h2 {
		padding: 2rem 2rem 1rem 2rem;
		margin: 0;
		font-weight: normal;
	}

	> div {
		padding: 1em 2em 2em 2em;
	}
}

div.dtb-popover-close {
	position: absolute;
	top: 6px;
	right: 6px;
	width: 22px;
	height: 22px;
	text-align: center;
	border-radius: 3px;
	cursor: pointer;
	z-index: 2003;
}

button.dtb-hide-drop {
	display: none !important;
}

div.dt-button-collection-title {
	text-align: center;
	padding: 0.3em 0 0.5em;
	margin-left: 0.5em;
	margin-right: 0.5em;
	font-size: 0.9em;
}

div.dt-button-collection-title:empty {
	display: none;
}

span.dt-button-spacer {
	display: inline-block;
	margin: 0.5em;
	white-space: nowrap;

	&.bar {
		border-left: 1px solid rgba(0, 0, 0, 0.3);
		vertical-align: middle;
		padding-left: 0.5em;

		&:empty {
			height: 1em;
			width: 1px;
			padding-left: 0;
		}
	}
}



div.dt-button-collection {
	.dt-button-active {
		padding-right: 3em;

		&:after {
			@include active-tick;
		}

		&.dt-button-split {
			padding-right: 0;

			&:after {
				display: none;
			}

			> *:first-child {
				padding-right: 3em;
		
				&:after {
					@include active-tick;
				}
			}
		}
	}

	.dt-button-active-a a {
		padding-right: 3em;

		&:after {
			position: absolute;
			right: 1em;
			display: inline-block;
			content: '\2713'; // utf8 tick
			color: inherit;
		}
	}

	span.dt-button-spacer {
		width: 100%;
		font-size: 0.9em;
		text-align: center;
		margin: 0.5em 0;

		&:empty {
			height: 0;
			width: 100%;
		}

		&.bar {
			border-left: none;
			border-bottom: 1px solid rgba(0, 0, 0, 0.1);
			padding-left: 0;
		}
	}
}

html.dark {
	div.dt-button-info {
		background-color: var(--dt-html-background);
		border: 1px solid rgba(255, 255, 255, 0.15);
	}
}
