/*! Bootstrap 4 ui integration for DataTables' SearchBuilder
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var d,r;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bs4","datatables.net-searchbuilder"],function(t){return n(t,window,document)}):"object"==typeof exports?(d=require("jquery"),r=function(t,e){e.fn.dataTable||require("datatables.net-bs4")(t,e),e.fn.dataTable.SearchBuilder||require("datatables.net-searchbuilder")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||d(t),r(t,e),n(e,0,t.document)}:(r(window,d),module.exports=n(d,window,window.document))):n(jQuery,window,document)}(function(t,e,n,d){"use strict";var r=t.fn.dataTable;return t.extend(!0,r.SearchBuilder.classes,{clearAll:"btn btn-light dtsb-clearAll"}),t.extend(!0,r.Group.classes,{add:"btn btn-light dtsb-add",clearGroup:"btn btn-light dtsb-clearGroup",logic:"btn btn-light dtsb-logic"}),t.extend(!0,r.Criteria.classes,{condition:"form-control dtsb-condition",data:"form-control dtsb-data",delete:"btn btn-light dtsb-delete",left:"btn btn-light dtsb-left",right:"btn btn-light dtsb-right",value:"form-control dtsb-value"}),r});