/*! Bulma ui integration for DataTables' SearchBuilder
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var d,r;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bm","datatables.net-searchbuilder"],function(e){return n(e,window,document)}):"object"==typeof exports?(d=require("jquery"),r=function(e,t){t.fn.dataTable||require("datatables.net-bm")(e,t),t.fn.dataTable.SearchBuilder||require("datatables.net-searchbuilder")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||d(e),r(e,t),n(t,0,e.document)}:(r(window,d),module.exports=n(d,window,window.document))):n(jQuery,window,document)}(function(e,t,n,d){"use strict";var r=e.fn.dataTable;return e.extend(!0,r.SearchBuilder.classes,{clearAll:"button dtsb-clearAll"}),e.extend(!0,r.Group.classes,{add:"button dtsb-add",clearGroup:"button dtsb-clearGroup is-light",logic:"button dtsb-logic is-light"}),e.extend(!0,r.Criteria.classes,{container:"dtsb-criteria",delete:"button dtsb-delete",left:"button dtsb-left",right:"button dtsb-right"}),r});