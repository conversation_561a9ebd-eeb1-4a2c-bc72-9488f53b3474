/*! Bootstrap ui integration for DataTables' SearchBuilder
 * © SpryMedia Ltd - datatables.net/license
*/
!function(n){var d,a;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bs","datatables.net-searchbuilder"],function(t){return n(t,window,document)}):"object"==typeof exports?(d=require("jquery"),a=function(t,e){e.fn.dataTable||require("datatables.net-bs")(t,e),e.fn.dataTable.SearchBuilder||require("datatables.net-searchbuilder")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||d(t),a(t,e),n(e,0,t.document)}:(a(window,d),module.exports=n(d,window,window.document))):n(jQuery,window,document)}(function(t,e,n,d){"use strict";var a=t.fn.dataTable;return t.extend(!0,a.SearchBuilder.classes,{clearAll:"btn btn-default dtsb-clearAll"}),t.extend(!0,a.Group.classes,{add:"btn btn-default dtsb-add",clearGroup:"btn btn-default dtsb-clearGroup",logic:"btn btn-default dtsb-logic"}),t.extend(!0,a.Criteria.classes,{condition:"form-control dtsb-condition",data:"form-control dtsb-data",delete:"btn btn-default dtsb-delete",left:"btn btn-default dtsb-left",right:"btn btn-default dtsb-right",value:"form-control dtsb-value"}),a});