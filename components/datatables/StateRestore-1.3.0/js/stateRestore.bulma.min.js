/*! Bootstrap integration for DataTables' StateRestore
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var o,r;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bm","datatables.net-staterestore"],function(t){return n(t,window,document)}):"object"==typeof exports?(o=require("jquery"),r=function(t,e){e.fn.dataTable||require("datatables.net-bm")(t,e),e.fn.dataTable.StateRestore||require("datatables.net-staterestore")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||o(t),r(t,e),n(e,0,t.document)}:(r(window,o),module.exports=n(o,window,window.document))):n(jQuery,window,document)}(function(t,e,n,o){"use strict";var r=t.fn.dataTable;return t.extend(!0,r.StateRestoreCollection.classes,{checkRow:"dtsr-check-row checkbox",creationButton:"dtsr-creation-button button",creationForm:"dtsr-creation-form modal-content",creationText:"dtsr-creation-text modal-header",creationTitle:"dtsr-creation-title modal-card-title",nameInput:"dtsr-name-input input"}),t.extend(!0,r.StateRestore.classes,{confirmationButton:"dtsr-confirmation-button button",confirmationTitle:"dtsr-confirmation-title modal-card-title",input:"dtsr-input input"}),r});