/*! Bootstrap integration for DataTables' StateRestore
 * © SpryMedia Ltd - datatables.net/license
 */
!function(o){var n,r;"function"==typeof define&&define.amd?define(["jquery","datatables.net-jqui","datatables.net-staterestore"],function(e){return o(e,window,document)}):"object"==typeof exports?(n=require("jquery"),r=function(e,t){t.fn.dataTable||require("datatables.net-jqui")(e,t),t.fn.dataTable.StateRestore||require("datatables.net-staterestore")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||n(e),r(e,t),o(t,0,e.document)}:(r(window,n),module.exports=o(n,window,window.document))):o(jQuery,window,document)}(function(e,t,o,n){"use strict";var r=e.fn.dataTable;return e.extend(!0,r.StateRestoreCollection.classes,{checkBox:"dtsr-check-box form-check-input",checkLabel:"dtsr-check-label form-check-label",checkRow:"dtsr-check-row form",creationButton:"dtsr-creation-button ui-button ui-corner-all ui-widget",creationForm:"dtsr-creation-form modal-body",creationText:"dtsr-creation-text modal-header",creationTitle:"dtsr-creation-title modal-title",nameInput:"dtsr-name-input form-control",nameLabel:"dtsr-name-label form-label",nameRow:"dtsr-name-row medium-6 cell"}),e.extend(!0,r.StateRestore.classes,{confirmationButton:"dtsr-confirmation-button ui-button ui-state-default ui-button-text-only ui-corner-all ui-widget"}),r});