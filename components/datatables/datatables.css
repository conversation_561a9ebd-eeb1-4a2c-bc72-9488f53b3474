/*
 * This combined file was created by the DataTables downloader builder:
 *   https://datatables.net/download
 *
 * To rebuild or modify this file with the latest versions of the included
 * software please visit:
 *   https://datatables.net/download/#bs5/jszip-3.10.1/pdfmake-0.2.7/dt-1.13.8/af-2.6.0/b-2.4.2/b-colvis-2.4.2/b-html5-2.4.2/b-print-2.4.2/cr-1.7.0/date-1.5.1/fc-4.3.0/fh-3.4.0/kt-2.11.0/r-2.5.0/rg-1.4.1/rr-1.4.1/sc-2.3.0/sb-1.6.0/sp-2.2.0/sl-1.7.0/sr-1.3.0
 *
 * Included libraries:
 *   JSZip 3.10.1, pdfmake 0.2.7, DataTables 1.13.8, AutoFill 2.6.0, Buttons 2.4.2, Column visibility 2.4.2, HTML5 export 2.4.2, Print view 2.4.2, ColReorder 1.7.0, DateTime 1.5.1, FixedColumns 4.3.0, FixedHeader 3.4.0, KeyTable 2.11.0, Responsive 2.5.0, RowGroup 1.4.1, RowReorder 1.4.1, Scroller 2.3.0, SearchBuilder 1.6.0, SearchPanes 2.2.0, Select 1.7.0, StateRestore 1.3.0
 */

@charset "UTF-8";
:root {
  --dt-row-selected: 13, 110, 253;
  --dt-row-selected-text: 255, 255, 255;
  --dt-row-selected-link: 9, 10, 11;
  --dt-row-stripe: 0, 0, 0;
  --dt-row-hover: 0, 0, 0;
  --dt-column-ordering: 0, 0, 0;
  --dt-html-background: white;
}
:root.dark {
  --dt-html-background: rgb(33, 37, 41);
}

table.dataTable td.dt-control {
  text-align: center;
  cursor: pointer;
}
table.dataTable td.dt-control:before {
  display: inline-block;
  color: rgba(0, 0, 0, 0.5);
  content: "▶";
}
table.dataTable tr.dt-hasChild td.dt-control:before {
  content: "▼";
}

html.dark table.dataTable td.dt-control:before {
  color: rgba(255, 255, 255, 0.5);
}
html.dark table.dataTable tr.dt-hasChild td.dt-control:before {
  color: rgba(255, 255, 255, 0.5);
}

table.dataTable thead > tr > th.sorting, table.dataTable thead > tr > th.sorting_asc, table.dataTable thead > tr > th.sorting_desc, table.dataTable thead > tr > th.sorting_asc_disabled, table.dataTable thead > tr > th.sorting_desc_disabled,
table.dataTable thead > tr > td.sorting,
table.dataTable thead > tr > td.sorting_asc,
table.dataTable thead > tr > td.sorting_desc,
table.dataTable thead > tr > td.sorting_asc_disabled,
table.dataTable thead > tr > td.sorting_desc_disabled {
  cursor: pointer;
  position: relative;
  padding-right: 26px;
}
table.dataTable thead > tr > th.sorting:before, table.dataTable thead > tr > th.sorting:after, table.dataTable thead > tr > th.sorting_asc:before, table.dataTable thead > tr > th.sorting_asc:after, table.dataTable thead > tr > th.sorting_desc:before, table.dataTable thead > tr > th.sorting_desc:after, table.dataTable thead > tr > th.sorting_asc_disabled:before, table.dataTable thead > tr > th.sorting_asc_disabled:after, table.dataTable thead > tr > th.sorting_desc_disabled:before, table.dataTable thead > tr > th.sorting_desc_disabled:after,
table.dataTable thead > tr > td.sorting:before,
table.dataTable thead > tr > td.sorting:after,
table.dataTable thead > tr > td.sorting_asc:before,
table.dataTable thead > tr > td.sorting_asc:after,
table.dataTable thead > tr > td.sorting_desc:before,
table.dataTable thead > tr > td.sorting_desc:after,
table.dataTable thead > tr > td.sorting_asc_disabled:before,
table.dataTable thead > tr > td.sorting_asc_disabled:after,
table.dataTable thead > tr > td.sorting_desc_disabled:before,
table.dataTable thead > tr > td.sorting_desc_disabled:after {
  position: absolute;
  display: block;
  opacity: 0.125;
  right: 10px;
  line-height: 9px;
  font-size: 0.8em;
}
table.dataTable thead > tr > th.sorting:before, table.dataTable thead > tr > th.sorting_asc:before, table.dataTable thead > tr > th.sorting_desc:before, table.dataTable thead > tr > th.sorting_asc_disabled:before, table.dataTable thead > tr > th.sorting_desc_disabled:before,
table.dataTable thead > tr > td.sorting:before,
table.dataTable thead > tr > td.sorting_asc:before,
table.dataTable thead > tr > td.sorting_desc:before,
table.dataTable thead > tr > td.sorting_asc_disabled:before,
table.dataTable thead > tr > td.sorting_desc_disabled:before {
  bottom: 50%;
  content: "▲";
  content: "▲"/"";
}
table.dataTable thead > tr > th.sorting:after, table.dataTable thead > tr > th.sorting_asc:after, table.dataTable thead > tr > th.sorting_desc:after, table.dataTable thead > tr > th.sorting_asc_disabled:after, table.dataTable thead > tr > th.sorting_desc_disabled:after,
table.dataTable thead > tr > td.sorting:after,
table.dataTable thead > tr > td.sorting_asc:after,
table.dataTable thead > tr > td.sorting_desc:after,
table.dataTable thead > tr > td.sorting_asc_disabled:after,
table.dataTable thead > tr > td.sorting_desc_disabled:after {
  top: 50%;
  content: "▼";
  content: "▼"/"";
}
table.dataTable thead > tr > th.sorting_asc:before, table.dataTable thead > tr > th.sorting_desc:after,
table.dataTable thead > tr > td.sorting_asc:before,
table.dataTable thead > tr > td.sorting_desc:after {
  opacity: 0.6;
}
table.dataTable thead > tr > th.sorting_desc_disabled:after, table.dataTable thead > tr > th.sorting_asc_disabled:before,
table.dataTable thead > tr > td.sorting_desc_disabled:after,
table.dataTable thead > tr > td.sorting_asc_disabled:before {
  display: none;
}
table.dataTable thead > tr > th:active,
table.dataTable thead > tr > td:active {
  outline: none;
}

div.dataTables_scrollBody > table.dataTable > thead > tr > th:before, div.dataTables_scrollBody > table.dataTable > thead > tr > th:after,
div.dataTables_scrollBody > table.dataTable > thead > tr > td:before,
div.dataTables_scrollBody > table.dataTable > thead > tr > td:after {
  display: none;
}

div.dataTables_processing {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200px;
  margin-left: -100px;
  margin-top: -26px;
  text-align: center;
  padding: 2px;
  z-index: 10;
}
div.dataTables_processing > div:last-child {
  position: relative;
  width: 80px;
  height: 15px;
  margin: 1em auto;
}
div.dataTables_processing > div:last-child > div {
  position: absolute;
  top: 0;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: rgb(13, 110, 253);
  background: rgb(var(--dt-row-selected));
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}
div.dataTables_processing > div:last-child > div:nth-child(1) {
  left: 8px;
  animation: datatables-loader-1 0.6s infinite;
}
div.dataTables_processing > div:last-child > div:nth-child(2) {
  left: 8px;
  animation: datatables-loader-2 0.6s infinite;
}
div.dataTables_processing > div:last-child > div:nth-child(3) {
  left: 32px;
  animation: datatables-loader-2 0.6s infinite;
}
div.dataTables_processing > div:last-child > div:nth-child(4) {
  left: 56px;
  animation: datatables-loader-3 0.6s infinite;
}

@keyframes datatables-loader-1 {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes datatables-loader-3 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}
@keyframes datatables-loader-2 {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(24px, 0);
  }
}
table.dataTable.nowrap th, table.dataTable.nowrap td {
  white-space: nowrap;
}
table.dataTable th.dt-left,
table.dataTable td.dt-left {
  text-align: left;
}
table.dataTable th.dt-center,
table.dataTable td.dt-center,
table.dataTable td.dataTables_empty {
  text-align: center;
}
table.dataTable th.dt-right,
table.dataTable td.dt-right {
  text-align: right;
}
table.dataTable th.dt-justify,
table.dataTable td.dt-justify {
  text-align: justify;
}
table.dataTable th.dt-nowrap,
table.dataTable td.dt-nowrap {
  white-space: nowrap;
}
table.dataTable thead th,
table.dataTable thead td,
table.dataTable tfoot th,
table.dataTable tfoot td {
  text-align: left;
}
table.dataTable thead th.dt-head-left,
table.dataTable thead td.dt-head-left,
table.dataTable tfoot th.dt-head-left,
table.dataTable tfoot td.dt-head-left {
  text-align: left;
}
table.dataTable thead th.dt-head-center,
table.dataTable thead td.dt-head-center,
table.dataTable tfoot th.dt-head-center,
table.dataTable tfoot td.dt-head-center {
  text-align: center;
}
table.dataTable thead th.dt-head-right,
table.dataTable thead td.dt-head-right,
table.dataTable tfoot th.dt-head-right,
table.dataTable tfoot td.dt-head-right {
  text-align: right;
}
table.dataTable thead th.dt-head-justify,
table.dataTable thead td.dt-head-justify,
table.dataTable tfoot th.dt-head-justify,
table.dataTable tfoot td.dt-head-justify {
  text-align: justify;
}
table.dataTable thead th.dt-head-nowrap,
table.dataTable thead td.dt-head-nowrap,
table.dataTable tfoot th.dt-head-nowrap,
table.dataTable tfoot td.dt-head-nowrap {
  white-space: nowrap;
}
table.dataTable tbody th.dt-body-left,
table.dataTable tbody td.dt-body-left {
  text-align: left;
}
table.dataTable tbody th.dt-body-center,
table.dataTable tbody td.dt-body-center {
  text-align: center;
}
table.dataTable tbody th.dt-body-right,
table.dataTable tbody td.dt-body-right {
  text-align: right;
}
table.dataTable tbody th.dt-body-justify,
table.dataTable tbody td.dt-body-justify {
  text-align: justify;
}
table.dataTable tbody th.dt-body-nowrap,
table.dataTable tbody td.dt-body-nowrap {
  white-space: nowrap;
}

/*! Bootstrap 5 integration for DataTables
 *
 * ©2020 SpryMedia Ltd, all rights reserved.
 * License: MIT datatables.net/license/mit
 */
table.dataTable {
  clear: both;
  margin-top: 6px !important;
  margin-bottom: 6px !important;
  max-width: none !important;
  border-collapse: separate !important;
  border-spacing: 0;
}
table.dataTable td,
table.dataTable th {
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}
table.dataTable td.dataTables_empty,
table.dataTable th.dataTables_empty {
  text-align: center;
}
table.dataTable.nowrap th,
table.dataTable.nowrap td {
  white-space: nowrap;
}
table.dataTable.table-striped > tbody > tr:nth-of-type(2n+1) > * {
  box-shadow: none;
}
table.dataTable > tbody > tr {
  background-color: transparent;
}
table.dataTable > tbody > tr.selected > * {
  box-shadow: inset 0 0 0 9999px rgb(13, 110, 253);
  box-shadow: inset 0 0 0 9999px rgb(var(--dt-row-selected));
  color: rgb(255, 255, 255);
  color: rgb(var(--dt-row-selected-text));
}
table.dataTable > tbody > tr.selected a {
  color: rgb(9, 10, 11);
  color: rgb(var(--dt-row-selected-link));
}
table.dataTable.table-striped > tbody > tr:nth-of-type(2n+1) > * {
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-stripe), 0.05);
}
table.dataTable.table-striped > tbody > tr:nth-of-type(2n+1).selected > * {
  box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.95);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-selected), 0.95);
}
table.dataTable.table-hover > tbody > tr:hover > * {
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-hover), 0.075);
}
table.dataTable.table-hover > tbody > tr.selected:hover > * {
  box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.975);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-selected), 0.975);
}

div.dataTables_wrapper div.dataTables_length label {
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
}
div.dataTables_wrapper div.dataTables_length select {
  width: auto;
  display: inline-block;
}
div.dataTables_wrapper div.dataTables_filter {
  text-align: right;
}
div.dataTables_wrapper div.dataTables_filter label {
  font-weight: normal;
  white-space: nowrap;
  text-align: left;
}
div.dataTables_wrapper div.dataTables_filter input {
  margin-left: 0.5em;
  display: inline-block;
  width: auto;
}
div.dataTables_wrapper div.dataTables_info {
  padding-top: 0.85em;
}
div.dataTables_wrapper div.dataTables_paginate {
  margin: 0;
  white-space: nowrap;
  text-align: right;
}
div.dataTables_wrapper div.dataTables_paginate ul.pagination {
  margin: 2px 0;
  white-space: nowrap;
  justify-content: flex-end;
}
div.dataTables_wrapper div.dt-row {
  position: relative;
}

div.dataTables_scrollHead table.dataTable {
  margin-bottom: 0 !important;
}

div.dataTables_scrollBody > table {
  border-top: none;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
div.dataTables_scrollBody > table > thead .sorting:before,
div.dataTables_scrollBody > table > thead .sorting_asc:before,
div.dataTables_scrollBody > table > thead .sorting_desc:before,
div.dataTables_scrollBody > table > thead .sorting:after,
div.dataTables_scrollBody > table > thead .sorting_asc:after,
div.dataTables_scrollBody > table > thead .sorting_desc:after {
  display: none;
}
div.dataTables_scrollBody > table > tbody tr:first-child th,
div.dataTables_scrollBody > table > tbody tr:first-child td {
  border-top: none;
}

div.dataTables_scrollFoot > .dataTables_scrollFootInner {
  box-sizing: content-box;
}
div.dataTables_scrollFoot > .dataTables_scrollFootInner > table {
  margin-top: 0 !important;
  border-top: none;
}

@media screen and (max-width: 767px) {
  div.dataTables_wrapper div.dataTables_length,
  div.dataTables_wrapper div.dataTables_filter,
  div.dataTables_wrapper div.dataTables_info,
  div.dataTables_wrapper div.dataTables_paginate {
    text-align: center;
  }
  div.dataTables_wrapper div.dataTables_paginate ul.pagination {
    justify-content: center !important;
  }
}
table.dataTable.table-sm > thead > tr > th:not(.sorting_disabled) {
  padding-right: 20px;
}
table.dataTable.table-sm > thead > tr > th:not(.sorting_disabled):before, table.dataTable.table-sm > thead > tr > th:not(.sorting_disabled):after {
  right: 5px;
}

table.table-bordered.dataTable {
  border-right-width: 0;
}
table.table-bordered.dataTable thead tr:first-child th,
table.table-bordered.dataTable thead tr:first-child td {
  border-top-width: 1px;
}
table.table-bordered.dataTable th,
table.table-bordered.dataTable td {
  border-left-width: 0;
}
table.table-bordered.dataTable th:first-child, table.table-bordered.dataTable th:first-child,
table.table-bordered.dataTable td:first-child,
table.table-bordered.dataTable td:first-child {
  border-left-width: 1px;
}
table.table-bordered.dataTable th:last-child, table.table-bordered.dataTable th:last-child,
table.table-bordered.dataTable td:last-child,
table.table-bordered.dataTable td:last-child {
  border-right-width: 1px;
}
table.table-bordered.dataTable th,
table.table-bordered.dataTable td {
  border-bottom-width: 1px;
}

div.dataTables_scrollHead table.table-bordered {
  border-bottom-width: 0;
}

div.table-responsive > div.dataTables_wrapper > div.row {
  margin: 0;
}
div.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:first-child {
  padding-left: 0;
}
div.table-responsive > div.dataTables_wrapper > div.row > div[class^=col-]:last-child {
  padding-right: 0;
}

:root[data-bs-theme=dark] {
  --dt-row-hover: 255, 255, 255;
  --dt-row-stripe: 255, 255, 255;
  --dt-column-ordering: 255, 255, 255;
}


div.dt-autofill-handle{position:absolute;height:8px;width:8px;z-index:10;box-sizing:border-box;background:#0d6efd;cursor:pointer}div.dtk-focus-alt div.dt-autofill-handle{background:#ff8b33}div.dt-autofill-select{position:absolute;z-index:1001;background-color:#0d6efd;background-image:repeating-linear-gradient(45deg, transparent, transparent 5px, rgba(255, 255, 255, 0.5) 5px, rgba(255, 255, 255, 0.5) 10px)}div.dt-autofill-select.top,div.dt-autofill-select.bottom{height:3px;margin-top:-1px}div.dt-autofill-select.left,div.dt-autofill-select.right{width:3px;margin-left:-1px}div.dt-autofill-list{position:fixed;top:50%;left:50%;width:500px;margin-left:-250px;background-color:white;border-radius:.75em;box-shadow:0 12px 30px rgba(0, 0, 0, 0.6);z-index:104;box-sizing:border-box;padding:2em}div.dt-autofill-list div.dtaf-popover-close{position:absolute;top:6px;right:6px;width:22px;height:22px;text-align:center;border-radius:3px;cursor:pointer;z-index:12}div.dt-autofill-list>div.dt-autofill-list-items>button{display:block;width:100%;margin:1em 0;padding:1em;border-radius:.5em;border:1px solid rgba(0, 0, 0, 0.175);background-color:#f6f6f6;text-align:left;cursor:pointer}div.dt-autofill-list>div.dt-autofill-list-items>button:hover{background-color:#ebebeb}div.dt-autofill-list>div.dt-autofill-list-items>button:first-child{margin-top:0}div.dt-autofill-list>div.dt-autofill-list-items>button:last-child{margin-bottom:0}div.dt-autofill-list>div.dt-autofill-list-items>button input[type=number]{padding:6px;width:30px;margin:-2px 0}div.dt-autofill-list>div.dt-autofill-list-items>button span{float:right}div.dtaf-popover-closeable{padding-top:2.5em}div.dt-autofill-background{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0, 0, 0, 0.7);background:radial-gradient(ellipse farthest-corner at center, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);z-index:103}html.dark div.dt-autofill-handle{background:rgb(110, 168, 254)}html.dark div.dt-autofill-select{position:absolute;z-index:1001;background-color:rgb(110, 168, 254);background-image:repeating-linear-gradient(45deg, transparent, transparent 5px, rgba(0, 0, 0, 0.5) 5px, rgba(0, 0, 0, 0.5) 10px)}html.dark div.dt-autofill-list{background-color:var(--dt-html-background);border:1px solid rgba(255, 255, 255, 0.15)}html.dark div.dt-autofill-list button{color:inherit;border:1px solid rgba(255, 255, 255, 0.175);background-color:rgb(47, 52, 56)}html.dark div.dt-autofill-list button:hover{background-color:rgb(64, 69, 73)}@media screen and (max-width: 767px){div.dt-autofill-handle{height:16px;width:16px}div.dt-autofill-list{width:90%;left:74.5%}}div.dt-autofill-list div.dt-autofill-question input[type=number]{padding:6px;width:60px;margin:-2px 0}div.row.dt-row>div.col-sm-12{position:relative}


@keyframes dtb-spinner {
  100% {
    transform: rotate(360deg);
  }
}
@-o-keyframes dtb-spinner {
  100% {
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-ms-keyframes dtb-spinner {
  100% {
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes dtb-spinner {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-moz-keyframes dtb-spinner {
  100% {
    -moz-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
div.dataTables_wrapper {
  position: relative;
}

div.dt-buttons {
  position: initial;
}
div.dt-buttons .dt-button {
  overflow: hidden;
  text-overflow: ellipsis;
}

div.dt-button-info {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 400px;
  margin-top: -100px;
  margin-left: -200px;
  background-color: white;
  border-radius: 0.75em;
  box-shadow: 3px 4px 10px 1px rgba(0, 0, 0, 0.8);
  text-align: center;
  z-index: 2003;
  overflow: hidden;
}
div.dt-button-info h2 {
  padding: 2rem 2rem 1rem 2rem;
  margin: 0;
  font-weight: normal;
}
div.dt-button-info > div {
  padding: 1em 2em 2em 2em;
}

div.dtb-popover-close {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 22px;
  height: 22px;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
  z-index: 2003;
}

button.dtb-hide-drop {
  display: none !important;
}

div.dt-button-collection-title {
  text-align: center;
  padding: 0.3em 0 0.5em;
  margin-left: 0.5em;
  margin-right: 0.5em;
  font-size: 0.9em;
}

div.dt-button-collection-title:empty {
  display: none;
}

span.dt-button-spacer {
  display: inline-block;
  margin: 0.5em;
  white-space: nowrap;
}
span.dt-button-spacer.bar {
  border-left: 1px solid rgba(0, 0, 0, 0.3);
  vertical-align: middle;
  padding-left: 0.5em;
}
span.dt-button-spacer.bar:empty {
  height: 1em;
  width: 1px;
  padding-left: 0;
}

div.dt-button-collection .dt-button-active {
  padding-right: 3em;
}
div.dt-button-collection .dt-button-active:after {
  position: absolute;
  top: 50%;
  margin-top: -10px;
  right: 1em;
  display: inline-block;
  content: "✓";
  color: inherit;
}
div.dt-button-collection .dt-button-active.dt-button-split {
  padding-right: 0;
}
div.dt-button-collection .dt-button-active.dt-button-split:after {
  display: none;
}
div.dt-button-collection .dt-button-active.dt-button-split > *:first-child {
  padding-right: 3em;
}
div.dt-button-collection .dt-button-active.dt-button-split > *:first-child:after {
  position: absolute;
  top: 50%;
  margin-top: -10px;
  right: 1em;
  display: inline-block;
  content: "✓";
  color: inherit;
}
div.dt-button-collection .dt-button-active-a a {
  padding-right: 3em;
}
div.dt-button-collection .dt-button-active-a a:after {
  position: absolute;
  right: 1em;
  display: inline-block;
  content: "✓";
  color: inherit;
}
div.dt-button-collection span.dt-button-spacer {
  width: 100%;
  font-size: 0.9em;
  text-align: center;
  margin: 0.5em 0;
}
div.dt-button-collection span.dt-button-spacer:empty {
  height: 0;
  width: 100%;
}
div.dt-button-collection span.dt-button-spacer.bar {
  border-left: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-left: 0;
}

html.dark div.dt-button-info {
  background-color: var(--dt-html-background);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

div.dt-buttons div.btn-group {
  position: initial;
}
div.dt-buttons div.dropdown-menu {
  margin-top: 4px;
}
div.dt-buttons div.dropdown-menu .dt-button {
  position: relative;
}
div.dt-buttons div.dropdown-menu div.dt-button-split {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: stretch;
}
div.dt-buttons div.dropdown-menu div.dt-button-split a:first-child {
  min-width: auto;
  flex: 1 0 50px;
  padding-right: 0;
}
div.dt-buttons div.dropdown-menu div.dt-button-split button:last-child {
  min-width: 33px;
  flex: 0;
  background: transparent;
  border: none;
  line-height: 1rem;
  color: var(--bs-dropdown-link-color);
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
}
div.dt-buttons div.dropdown-menu div.dt-button-split button:last-child:hover {
  color: var(--bs-dropdown-link-hover-color);
  background-color: var(--bs-dropdown-link-hover-bg);
}
div.dt-buttons div.dropdown-menu div.dt-button-split button:last-child:after {
  position: relative;
  left: -3px;
}
div.dt-buttons div.dropdown-menu.fixed {
  position: fixed;
  display: block;
  top: 50%;
  left: 50%;
  margin-left: -75px;
  border-radius: 5px;
  background-color: white;
  padding: 0.5em;
}
div.dt-buttons div.dropdown-menu.fixed.two-column {
  margin-left: -200px;
}
div.dt-buttons div.dropdown-menu.fixed.three-column {
  margin-left: -225px;
}
div.dt-buttons div.dropdown-menu.fixed.four-column {
  margin-left: -300px;
}
div.dt-buttons div.dropdown-menu.fixed.columns {
  margin-left: -409px;
}
@media screen and (max-width: 1024px) {
  div.dt-buttons div.dropdown-menu.fixed.columns {
    margin-left: -308px;
  }
}
@media screen and (max-width: 640px) {
  div.dt-buttons div.dropdown-menu.fixed.columns {
    margin-left: -203px;
  }
}
@media screen and (max-width: 460px) {
  div.dt-buttons div.dropdown-menu.fixed.columns {
    margin-left: -100px;
  }
}
div.dt-buttons div.dropdown-menu.fixed > :last-child {
  max-height: 100vh;
  overflow: auto;
}
div.dt-buttons div.dropdown-menu.two-column > :last-child, div.dt-buttons div.dropdown-menu.three-column > :last-child, div.dt-buttons div.dropdown-menu.four-column > :last-child {
  display: block !important;
  -webkit-column-gap: 8px;
  -moz-column-gap: 8px;
  -ms-column-gap: 8px;
  -o-column-gap: 8px;
  column-gap: 8px;
}
div.dt-buttons div.dropdown-menu.two-column > :last-child > *, div.dt-buttons div.dropdown-menu.three-column > :last-child > *, div.dt-buttons div.dropdown-menu.four-column > :last-child > * {
  -webkit-column-break-inside: avoid;
  break-inside: avoid;
}
div.dt-buttons div.dropdown-menu.two-column {
  width: 400px;
}
div.dt-buttons div.dropdown-menu.two-column > :last-child {
  padding-bottom: 1px;
  column-count: 2;
}
div.dt-buttons div.dropdown-menu.three-column {
  width: 450px;
}
div.dt-buttons div.dropdown-menu.three-column > :last-child {
  padding-bottom: 1px;
  column-count: 3;
}
div.dt-buttons div.dropdown-menu.four-column {
  width: 600px;
}
div.dt-buttons div.dropdown-menu.four-column > :last-child {
  padding-bottom: 1px;
  column-count: 4;
}
div.dt-buttons div.dropdown-menu .dt-button {
  border-radius: 0;
}
div.dt-buttons div.dropdown-menu.columns {
  width: auto;
}
div.dt-buttons div.dropdown-menu.columns > :last-child {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  gap: 6px;
  width: 818px;
  padding-bottom: 1px;
}
div.dt-buttons div.dropdown-menu.columns > :last-child .dt-button {
  min-width: 200px;
  flex: 0 1;
  margin: 0;
}
div.dt-buttons div.dropdown-menu.columns.dtb-b3 > :last-child, div.dt-buttons div.dropdown-menu.columns.dtb-b2 > :last-child, div.dt-buttons div.dropdown-menu.columns.dtb-b1 > :last-child {
  justify-content: space-between;
}
div.dt-buttons div.dropdown-menu.columns.dtb-b3 .dt-button {
  flex: 1 1 32%;
}
div.dt-buttons div.dropdown-menu.columns.dtb-b2 .dt-button {
  flex: 1 1 48%;
}
div.dt-buttons div.dropdown-menu.columns.dtb-b1 .dt-button {
  flex: 1 1 100%;
}
@media screen and (max-width: 1024px) {
  div.dt-buttons div.dropdown-menu.columns > :last-child {
    width: 612px;
  }
}
@media screen and (max-width: 640px) {
  div.dt-buttons div.dropdown-menu.columns > :last-child {
    width: 406px;
  }
  div.dt-buttons div.dropdown-menu.columns.dtb-b3 .dt-button {
    flex: 0 1 32%;
  }
}
@media screen and (max-width: 460px) {
  div.dt-buttons div.dropdown-menu.columns > :last-child {
    width: 200px;
  }
}
div.dt-buttons span.dt-button-spacer.empty {
  margin: 1px;
}
div.dt-buttons span.dt-button-spacer.bar:empty {
  height: inherit;
}
div.dt-buttons .btn.processing {
  color: rgba(0, 0, 0, 0.2);
}
div.dt-buttons .btn.processing:after {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  box-sizing: border-box;
  display: block;
  content: " ";
  border: 2px solid rgb(40, 40, 40);
  border-radius: 50%;
  border-left-color: transparent;
  border-right-color: transparent;
  animation: dtb-spinner 1500ms infinite linear;
  -o-animation: dtb-spinner 1500ms infinite linear;
  -ms-animation: dtb-spinner 1500ms infinite linear;
  -webkit-animation: dtb-spinner 1500ms infinite linear;
  -moz-animation: dtb-spinner 1500ms infinite linear;
}

div.dt-button-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}

@media screen and (max-width: 767px) {
  div.dt-buttons {
    float: none;
    width: 100%;
    text-align: center;
    margin-bottom: 0.5em;
  }
  div.dt-buttons a.btn {
    float: none;
  }
}
:root[data-bs-theme=dark] div.dropdown-menu.dt-button-collection.fixed {
  background-color: rgb(33, 37, 41);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
}


table.DTCR_clonedTable.dataTable {
  position: absolute !important;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 202;
  border-radius: 4px;
}

div.DTCR_pointer {
  width: 1px;
  background-color: #0d6efd;
  z-index: 201;
}

html.dark table.DTCR_clonedTable.dataTable {
  background-color: rgba(33, 33, 33, 0.9);
}
html.dark div.DTCR_pointer {
  background-color: rgb(13, 110, 253);
}


div.dt-datetime {
  position: absolute;
  background-color: white;
  z-index: 2050;
  border: 1px solid #ccc;
  box-shadow: 0 5px 15px -5px rgba(0, 0, 0, 0.5);
  padding: 6px 20px;
  width: 275px;
  border-radius: 5px;
}
div.dt-datetime.inline {
  position: relative;
  box-shadow: none;
}
div.dt-datetime div.dt-datetime-title {
  text-align: center;
  padding: 5px 0px 3px;
}
div.dt-datetime div.dt-datetime-buttons {
  text-align: center;
}
div.dt-datetime div.dt-datetime-buttons a {
  display: inline-block;
  padding: 0 0.5em 0.5em 0.5em;
  margin: 0;
  font-size: 0.9em;
}
div.dt-datetime div.dt-datetime-buttons a:hover {
  text-decoration: underline;
}
div.dt-datetime table {
  border-spacing: 0;
  margin: 12px 0;
  width: 100%;
}
div.dt-datetime table.dt-datetime-table-nospace {
  margin-top: -12px;
}
div.dt-datetime table th {
  font-size: 0.8em;
  color: #777;
  font-weight: normal;
  width: 14.285714286%;
  padding: 0 0 4px 0;
  text-align: center;
}
div.dt-datetime table td {
  font-size: 0.9em;
  color: #444;
  padding: 0;
}
div.dt-datetime table td.selectable {
  text-align: center;
  background: #f5f5f5;
}
div.dt-datetime table td.selectable.disabled {
  color: #aaa;
  background: white;
}
div.dt-datetime table td.selectable.disabled button:hover {
  color: #aaa;
  background: white;
}
div.dt-datetime table td.selectable.now {
  background-color: #ddd;
}
div.dt-datetime table td.selectable.now button {
  font-weight: bold;
}
div.dt-datetime table td.selectable.selected button {
  background: #4E6CA3;
  color: white;
  border-radius: 2px;
}
div.dt-datetime table td.selectable button:hover {
  background: #ff8000;
  color: white;
  border-radius: 2px;
}
div.dt-datetime table td.dt-datetime-week {
  font-size: 0.7em;
}
div.dt-datetime table button {
  width: 100%;
  box-sizing: border-box;
  border: none;
  background: transparent;
  font-size: inherit;
  color: inherit;
  text-align: center;
  padding: 4px 0;
  cursor: pointer;
  margin: 0;
}
div.dt-datetime table button span {
  display: inline-block;
  min-width: 14px;
  text-align: right;
}
div.dt-datetime table.weekNumber th {
  width: 12.5%;
}
div.dt-datetime div.dt-datetime-calendar table {
  margin-top: 0;
}
div.dt-datetime div.dt-datetime-label {
  position: relative;
  display: inline-block;
  height: 30px;
  padding: 5px 6px;
  border: 1px solid transparent;
  box-sizing: border-box;
  cursor: pointer;
}
div.dt-datetime div.dt-datetime-label:hover {
  border: 1px solid #ddd;
  border-radius: 2px;
  background-color: #f5f5f5;
}
div.dt-datetime div.dt-datetime-label select {
  position: absolute;
  top: 6px;
  left: 0;
  cursor: pointer;
  opacity: 0;
}
div.dt-datetime.horizontal {
  width: 550px;
}
div.dt-datetime.horizontal div.dt-datetime-date,
div.dt-datetime.horizontal div.dt-datetime-time {
  width: 48%;
}
div.dt-datetime.horizontal div.dt-datetime-time {
  margin-left: 4%;
}
div.dt-datetime div.dt-datetime-date {
  position: relative;
  float: left;
  width: 100%;
}
div.dt-datetime div.dt-datetime-time {
  position: relative;
  float: left;
  width: 100%;
  text-align: center;
}
div.dt-datetime div.dt-datetime-time > span {
  vertical-align: middle;
}
div.dt-datetime div.dt-datetime-time th {
  text-align: left;
}
div.dt-datetime div.dt-datetime-time div.dt-datetime-timeblock {
  display: inline-block;
  vertical-align: middle;
}
div.dt-datetime div.dt-datetime-iconLeft,
div.dt-datetime div.dt-datetime-iconRight {
  width: 30px;
  height: 30px;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.3;
  overflow: hidden;
  box-sizing: border-box;
  border: 1px solid transparent;
}
div.dt-datetime div.dt-datetime-iconLeft:hover,
div.dt-datetime div.dt-datetime-iconRight:hover {
  border: 1px solid #ccc;
  border-radius: 2px;
  background-color: #f0f0f0;
  opacity: 0.6;
}
div.dt-datetime div.dt-datetime-iconLeft button,
div.dt-datetime div.dt-datetime-iconRight button {
  border: none;
  background: transparent;
  text-indent: 30px;
  height: 100%;
  width: 100%;
  cursor: pointer;
}
div.dt-datetime div.dt-datetime-iconLeft {
  position: absolute;
  top: 5px;
  left: 5px;
}
div.dt-datetime div.dt-datetime-iconLeft button {
  position: relative;
  z-index: 1;
}
div.dt-datetime div.dt-datetime-iconLeft:after {
  position: absolute;
  top: 7px;
  left: 10px;
  display: block;
  content: "";
  border-top: 7px solid transparent;
  border-right: 7px solid black;
  border-bottom: 7px solid transparent;
}
div.dt-datetime div.dt-datetime-iconRight {
  position: absolute;
  top: 5px;
  right: 5px;
}
div.dt-datetime div.dt-datetime-iconRight button {
  position: relative;
  z-index: 1;
}
div.dt-datetime div.dt-datetime-iconRight:after {
  position: absolute;
  top: 7px;
  left: 12px;
  display: block;
  content: "";
  border-top: 7px solid transparent;
  border-left: 7px solid black;
  border-bottom: 7px solid transparent;
}

div.dt-datetime-error {
  clear: both;
  padding: 0 1em;
  max-width: 240px;
  font-size: 11px;
  line-height: 1.25em;
  text-align: center;
  color: #b11f1f;
}

html.dark input.dt-datetime {
  color-scheme: dark;
}
html.dark div.dt-datetime {
  border: 1px solid #595b5e;
  background-color: #212529;
  box-shadow: 3px 4px 10px 1px rgba(0, 0, 0, 0.8);
}
html.dark div.dt-datetime table th {
  color: #ccc;
}
html.dark div.dt-datetime table td {
  color: #eee;
}
html.dark div.dt-datetime table td.selectable {
  background: #373c41;
}
html.dark div.dt-datetime table td.selectable.disabled {
  color: #aaa;
  background: #171b1f;
}
html.dark div.dt-datetime table td.selectable.disabled button:hover {
  color: #aaa;
  background: #171b1f;
}
html.dark div.dt-datetime table td.selectable.now {
  background: #4b5055;
}
html.dark div.dt-datetime table td.selectable.selected button {
  background: #6ea8fe;
  color: black;
}
html.dark div.dt-datetime table td.selectable button:hover {
  background: #ff8000;
  color: black;
}
html.dark div.dt-datetime div.dt-datetime-label:hover {
  border: 1px solid transparent;
  background-color: rgba(255, 255, 255, 0.1);
}
html.dark div.dt-datetime div.dt-datetime-iconLeft:hover,
html.dark div.dt-datetime div.dt-datetime-iconRight:hover,
html.dark div.dt-datetime div.dt-datetime-iconUp:hover,
html.dark div.dt-datetime div.dt-datetime-iconDown:hover {
  border: 1px solid transparent;
  background-color: rgba(255, 255, 255, 0.1);
}
html.dark div.dt-datetime div.dt-datetime-iconLeft:after {
  border-right-color: white;
}
html.dark div.dt-datetime div.dt-datetime-iconRight:after {
  border-left-color: white;
}
html.dark div.dt-datetime select {
  color-scheme: dark;
}
html.dark div.dt-datetime-error {
  color: #b11f1f;
}

table.dataTable thead tr > .dtfc-fixed-left,
table.dataTable thead tr > .dtfc-fixed-right,
table.dataTable tfoot tr > .dtfc-fixed-left,
table.dataTable tfoot tr > .dtfc-fixed-right {
  top: 0;
  bottom: 0;
  z-index: 3;
  background-color: white;
}
table.dataTable tbody tr > .dtfc-fixed-left,
table.dataTable tbody tr > .dtfc-fixed-right {
  z-index: 1;
  background-color: white;
}

div.dtfc-left-top-blocker,
div.dtfc-right-top-blocker {
  background-color: white;
}

html.dark table.dataTable thead tr > .dtfc-fixed-left,
html.dark table.dataTable thead tr > .dtfc-fixed-right,
html.dark table.dataTable tfoot tr > .dtfc-fixed-left,
html.dark table.dataTable tfoot tr > .dtfc-fixed-right {
  background-color: var(--dt-html-background);
}
html.dark table.dataTable tbody tr > .dtfc-fixed-left,
html.dark table.dataTable tbody tr > .dtfc-fixed-right {
  background-color: var(--dt-html-background);
}
html.dark div.dtfc-left-top-blocker,
html.dark div.dtfc-right-top-blocker {
  background-color: var(--dt-html-background);
}

div.dtfc-right-top-blocker,
div.dtfc-left-top-blocker {
  margin-top: 6px;
  border-bottom: 0px solid #ddd !important;
}

table.dataTable.table-bordered.dtfc-has-left {
  border-left: none;
}

div.dataTables_scroll.dtfc-has-left table.table-bordered {
  border-left: none;
}

div.dataTables_scrollBody {
  border-left: 1px solid #ddd !important;
}

div.dataTables_scrollFootInner table.table-bordered tr th:first-child,
div.dataTables_scrollHeadInner table.table-bordered tr th:first-child {
  border-left: 1px solid #ddd !important;
}

html[data-bs-theme=dark] table.dataTable thead tr > .dtfc-fixed-left,
html[data-bs-theme=dark] table.dataTable thead tr > .dtfc-fixed-right,
html[data-bs-theme=dark] table.dataTable tfoot tr > .dtfc-fixed-left,
html[data-bs-theme=dark] table.dataTable tfoot tr > .dtfc-fixed-right {
  background-color: var(--bs-body-bg);
}
html[data-bs-theme=dark] table.dataTable tbody tr > .dtfc-fixed-left,
html[data-bs-theme=dark] table.dataTable tbody tr > .dtfc-fixed-right {
  background-color: var(--bs-body-bg);
}
html[data-bs-theme=dark] div.dtfc-left-top-blocker,
html[data-bs-theme=dark] div.dtfc-right-top-blocker {
  background-color: var(--bs-body-bg);
}
html[data-bs-theme=dark] div.dataTables_scrollBody {
  border-left-color: var(--bs-border-color) !important;
}
html[data-bs-theme=dark] div.dataTables_scrollFootInner table.table-bordered tr th:first-child,
html[data-bs-theme=dark] div.dataTables_scrollHeadInner table.table-bordered tr th:first-child {
  border-left-color: var(--bs-border-color) !important;
}


table.dataTable.fixedHeader-floating,
table.dataTable.fixedHeader-locked {
  background-color: white;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

table.dataTable.fixedHeader-locked {
  position: absolute !important;
}

@media print {
  table.fixedHeader-floating {
    display: none;
  }
}
html[data-bs-theme=dark] table.dataTable.fixedHeader-floating,
html[data-bs-theme=dark] table.dataTable.fixedHeader-locked {
  background-color: var(--bs-body-bg);
}


table.dataTable tbody th.focus,
table.dataTable tbody td.focus {
  outline: 2px solid #0d6efd;
  outline-offset: -2px;
}
table.dataTable tbody tr.selected th.focus,
table.dataTable tbody tr.selected td.focus {
  outline-color: #0143a3;
}

div.dtk-focus-alt table.dataTable tbody th.focus,
div.dtk-focus-alt table.dataTable tbody td.focus {
  outline: 2px solid #ff8b33;
  outline-offset: -2px;
}

html.dark table.dataTable tbody th.focus,
html.dark table.dataTable tbody td.focus {
  outline-color: rgb(13, 110, 253);
}
html.dark table.dataTable tbody tr.selected th.focus,
html.dark table.dataTable tbody tr.selected td.focus {
  outline-color: #0143a3;
}
html.dark div.dtk-focus-alt table.dataTable tbody th.focus,
html.dark div.dtk-focus-alt table.dataTable tbody td.focus {
  outline-color: #ff8b33;
}


table.dataTable.dtr-inline.collapsed > tbody > tr > td.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty {
  cursor: default !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty:before {
  display: none !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control {
  cursor: pointer;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
  margin-right: 0.5em;
  display: inline-block;
  color: rgba(0, 0, 0, 0.5);
  content: "►";
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control.arrow-right::before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control.arrow-right::before {
  content: "◄";
}
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th.dtr-control:before {
  content: "▼";
}
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th.dtr-control {
  padding-left: 0.333em;
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control,
table.dataTable.dtr-column > tbody > tr > th.dtr-control,
table.dataTable.dtr-column > tbody > tr > td.control,
table.dataTable.dtr-column > tbody > tr > th.control {
  cursor: pointer;
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr > th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr > td.control:before,
table.dataTable.dtr-column > tbody > tr > th.control:before {
  display: inline-block;
  color: rgba(0, 0, 0, 0.5);
  content: "►";
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control.arrow-right::before,
table.dataTable.dtr-column > tbody > tr > th.dtr-control.arrow-right::before,
table.dataTable.dtr-column > tbody > tr > td.control.arrow-right::before,
table.dataTable.dtr-column > tbody > tr > th.control.arrow-right::before {
  content: "◄";
}
table.dataTable.dtr-column > tbody > tr.parent td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent td.control:before,
table.dataTable.dtr-column > tbody > tr.parent th.control:before {
  content: "▼";
}
table.dataTable > tbody > tr.child {
  padding: 0.5em 1em;
}
table.dataTable > tbody > tr.child:hover {
  background: transparent !important;
}
table.dataTable > tbody > tr.child ul.dtr-details {
  display: inline-block;
  list-style-type: none;
  margin: 0;
  padding: 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li {
  border-bottom: 1px solid #efefef;
  padding: 0.5em 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:first-child {
  padding-top: 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:last-child {
  padding-bottom: 0;
  border-bottom: none;
}
table.dataTable > tbody > tr.child span.dtr-title {
  display: inline-block;
  min-width: 75px;
  font-weight: bold;
}

div.dtr-modal {
  position: fixed;
  box-sizing: border-box;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 100;
  padding: 10em 1em;
}
div.dtr-modal div.dtr-modal-display {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 50%;
  height: fit-content;
  max-height: 75%;
  overflow: auto;
  margin: auto;
  z-index: 102;
  overflow: auto;
  background-color: #f5f5f7;
  border: 1px solid black;
  border-radius: 0.5em;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.6);
}
div.dtr-modal div.dtr-modal-content {
  position: relative;
  padding: 2.5em;
}
div.dtr-modal div.dtr-modal-content h2 {
  margin-top: 0;
}
div.dtr-modal div.dtr-modal-close {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 22px;
  height: 22px;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
  z-index: 12;
}
div.dtr-modal div.dtr-modal-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 101;
  background: rgba(0, 0, 0, 0.6);
}

@media screen and (max-width: 767px) {
  div.dtr-modal div.dtr-modal-display {
    width: 95%;
  }
}
html.dark table.dataTable > tbody > tr > td.dtr-control:before {
  color: rgba(255, 255, 255, 0.5) !important;
}
html.dark table.dataTable > tbody > tr.child ul.dtr-details > li {
  border-bottom-color: rgb(64, 67, 70);
}
html.dark div.dtr-modal div.dtr-modal-display {
  background-color: rgb(33, 37, 41);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

div.dtr-bs-modal table.table tr:first-child td {
  border-top: none;
}

table.dataTable.table-bordered th.dtr-control.dtr-hidden + *,
table.dataTable.table-bordered td.dtr-control.dtr-hidden + * {
  border-left-width: 1px;
}


table.dataTable tr.dtrg-group th {
  background-color: rgba(0, 0, 0, 0.1);
  text-align: left;
}

table.dataTable tr.dtrg-group.dtrg-level-0 th {
  font-weight: bold;
}

table.dataTable tr.dtrg-group.dtrg-level-1 th,
table.dataTable tr.dtrg-group.dtrg-level-2 th,
table.dataTable tr.dtrg-group.dtrg-level-3 th,
table.dataTable tr.dtrg-group.dtrg-level-4 th,
table.dataTable tr.dtrg-group.dtrg-level-5 th {
  background-color: rgba(0, 0, 0, 0.05);
  padding-top: 0.25em;
  padding-bottom: 0.25em;
  padding-left: 2em;
  font-size: 0.9em;
}

table.dataTable tr.dtrg-group.dtrg-level-2 th {
  background-color: rgba(0, 0, 0, 0.01);
  padding-left: 2.5em;
}

table.dataTable tr.dtrg-group.dtrg-level-3 th {
  background-color: rgba(0, 0, 0, 0.01);
  padding-left: 3em;
}

table.dataTable tr.dtrg-group.dtrg-level-4 th {
  background-color: rgba(0, 0, 0, 0.01);
  padding-left: 3.5em;
}

table.dataTable tr.dtrg-group.dtrg-level-5 th {
  background-color: rgba(0, 0, 0, 0.01);
  padding-left: 4em;
}

html.dark table.dataTable tr.dtrg-group th {
  background-color: rgba(255, 255, 255, 0.1);
}
html.dark table.dataTable tr.dtrg-group.dtrg-level-1 th {
  background-color: rgba(255, 255, 255, 0.05);
}
html.dark table.dataTable tr.dtrg-group.dtrg-level-2 th,
html.dark table.dataTable tr.dtrg-group.dtrg-level-3 th,
html.dark table.dataTable tr.dtrg-group.dtrg-level-4 th,
html.dark table.dataTable tr.dtrg-group.dtrg-level-5 th {
  background-color: rgba(255, 255, 255, 0.01);
}

table.dataTable.table-striped tr.dtrg-level-0 {
  background-color: rgba(0, 0, 0, 0.1);
}
table.dataTable.table-striped tr.dtrg-level-1 {
  background-color: rgba(0, 0, 0, 0.05);
}
table.dataTable.table-striped tr.dtrg-level-2,
table.dataTable.table-striped tr.dtrg-level-3,
table.dataTable.table-striped tr.dtrg-level-4,
table.dataTable.table-striped tr.dtrg-level-5 {
  background-color: rgba(0, 0, 0, 0.01);
}
table.dataTable.table-striped tr.dtrg-level-1 tr.dtrg-level-2 th,
table.dataTable.table-striped tr.dtrg-level-3 th,
table.dataTable.table-striped tr.dtrg-level-4 th,
table.dataTable.table-striped tr.dtrg-level-5 th {
  background-color: transparent;
}


div.dt-rowReorder-float-parent {
  table-layout: fixed;
  outline: 2px solid #0d6efd;
  z-index: 2001;
  position: absolute !important;
  overflow: hidden;
  border-radius: 3px;
}
div.dt-rowReorder-float-parent table.dt-rowReorder-float {
  opacity: 0.9;
  background-color: white;
  margin: 0 !important;
}

div.dt-rowReorder-float-parent.drop-not-allowed {
  cursor: not-allowed;
}

tr.dt-rowReorder-moving {
  outline: 2px solid #888;
  outline-offset: -2px;
}

body.dt-rowReorder-noOverflow {
  overflow-x: hidden;
}

table.dataTable td.reorder {
  text-align: center;
  cursor: move;
}

html.dark div.dt-rowReorder-float-parent {
  outline-color: rgb(110, 168, 254);
}
html.dark div.dt-rowReorder-float-parent table.dt-rowReorder-float {
  background-color: var(--dt-html-background);
}
html.dark tr.dt-rowReorder-moving {
  outline-color: #aaa;
}

html[data-bs-theme=dark] div.dt-rowReorder-float-parent {
  outline-color: rgb(13, 110, 253);
}


div.dts {
  display: block !important;
}
div.dts tbody th,
div.dts tbody td {
  white-space: nowrap;
}
div.dts div.dts_loading {
  z-index: 1;
}
div.dts div.dts_label {
  position: absolute;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.5);
  text-align: right;
  border-radius: 3px;
  padding: 0.4em;
  z-index: 2;
  display: none;
}
div.dts div.dataTables_scrollBody {
  background: repeating-linear-gradient(45deg, rgba(0, 0, 0, 0.025), rgba(0, 0, 0, 0.025) 10px, rgba(0, 0, 0, 0) 10px, rgba(0, 0, 0, 0) 20px);
}
div.dts div.dataTables_scrollBody table {
  background-color: white;
  z-index: 2;
}
div.dts div.dt-length,
div.dts div.dt-paging,
div.dts div.dataTables_paginate,
div.dts div.dataTables_length {
  display: none;
}

html.dark div.dts div.dts_label {
  background: rgba(255, 255, 255, 0.8);
  color: black;
}
html.dark div.dts div.dataTables_scrollBody {
  background: repeating-linear-gradient(45deg, rgba(255, 255, 255, 0.025), rgba(255, 255, 255, 0.025) 10px, rgba(255, 255, 255, 0) 10px, rgba(255, 255, 255, 0) 20px);
}
html.dark div.dts div.dataTables_scrollBody table {
  background-color: var(--dt-html-background);
  z-index: 2;
}

div.DTS div.dataTables_scrollBody table {
  background-color: white;
}

html[data-bs-theme=dark] div.DTS div.dataTables_scrollBody table {
  background-color: var(--bs-body-bg);
}


div.dt-button-collection {
  overflow: visible !important;
  z-index: 2002 !important;
}
div.dt-button-collection div.dtsb-searchBuilder {
  padding-left: 1em !important;
  padding-right: 1em !important;
}

div.dt-button-collection.dtb-collection-closeable div.dtsb-titleRow {
  padding-right: 40px;
}

.dtsb-greyscale {
  border: 1px solid #cecece !important;
}

div.dtsb-logicContainer .dtsb-greyscale {
  border: none !important;
}

div.dtsb-searchBuilder {
  justify-content: space-evenly;
  cursor: default;
  margin-bottom: 1em;
  text-align: left;
}
div.dtsb-searchBuilder button.dtsb-button,
div.dtsb-searchBuilder select {
  font-size: 1em;
}
div.dtsb-searchBuilder div.dtsb-titleRow {
  justify-content: space-evenly;
  margin-bottom: 0.5em;
}
div.dtsb-searchBuilder div.dtsb-titleRow div.dtsb-title {
  display: inline-block;
  padding-top: 14px;
}
div.dtsb-searchBuilder div.dtsb-titleRow div.dtsb-title:empty {
  display: inline;
}
div.dtsb-searchBuilder div.dtsb-titleRow button.dtsb-clearAll {
  float: right;
  margin-bottom: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-vertical .dtsb-value, div.dtsb-searchBuilder div.dtsb-vertical .dtsb-data, div.dtsb-searchBuilder div.dtsb-vertical .dtsb-condition {
  display: block;
}
div.dtsb-searchBuilder div.dtsb-group {
  position: relative;
  clear: both;
  margin-bottom: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-group button.dtsb-search {
  float: right;
}
div.dtsb-searchBuilder div.dtsb-group button.dtsb-clearGroup {
  margin: 2px;
  text-align: center;
  padding: 0;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-logicContainer {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
  position: absolute;
  margin-top: 0.8em;
  margin-right: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria {
  margin-bottom: 0.8em;
  display: flex;
  justify-content: start;
  flex-flow: row wrap;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-dropDown,
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria input.dtsb-input {
  padding: 0.4em;
  margin-right: 0.8em;
  min-width: 5em;
  max-width: 20em;
  color: inherit;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-dropDown option.dtsb-notItalic,
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria input.dtsb-input option.dtsb-notItalic {
  font-style: normal;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-italic {
  font-style: italic;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-inputCont {
  flex: 1;
  white-space: nowrap;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-inputCont span.dtsp-joiner {
  margin-right: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-inputCont input.dtsb-value {
  width: 33%;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-inputCont select,
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-inputCont input {
  height: 100%;
  box-sizing: border-box;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer {
  margin-left: auto;
  display: inline-block;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-delete, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-right, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-left {
  margin-right: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-delete:last-child, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-right:last-child, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-left:last-child {
  margin-right: 0;
}
@media screen and (max-width: 550px) {
  div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria {
    display: flex;
    flex-flow: none;
    flex-direction: column;
    justify-content: start;
    padding-right: calc(35px + 0.8em);
    margin-bottom: 0px;
  }
  div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria:not(:first-child), div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria:not(:nth-child(2)), div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria:not(:last-child) {
    padding-top: 0.8em;
  }
  div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria:first-child, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria:nth-child(2), div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria:last-child {
    padding-top: 0em;
  }
  div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-dropDown,
  div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria input.dtsb-input {
    max-width: none;
    width: 100%;
    margin-bottom: 0.8em;
    margin-right: 0.8em;
  }
  div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-inputCont {
    margin-right: 0.8em;
  }
  div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer {
    position: absolute;
    width: 35px;
    display: flex;
    flex-wrap: wrap-reverse;
    right: 0;
  }
  div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button {
    margin-right: 0px !important;
  }
}

div.dtsb-searchBuilder div.dtsb-titleRow {
  height: 40px;
}
div.dtsb-searchBuilder div.dtsb-titleRow div.dtsb-title {
  padding-top: 10px;
}
div.dtsb-searchBuilder div.dtsb-group button.dtsb-clearGroup {
  margin-right: 8px;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria .form-select {
  width: auto;
  display: inline-block;
  padding-right: 30px !important;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-condition {
  border-color: #28a745;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-data {
  border-color: #dc3545;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-value, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria input.dtsb-value {
  border-color: #007bff;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria .form-control {
  display: inline-block;
  font-size: 1em;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-logicContainer {
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start;
  margin-top: 10px;
  overflow: hidden;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-logicContainer button.dtsb-logic {
  border: none;
  border-radius: 0px;
  flex-grow: 1;
  flex-shrink: 0;
  flex-basis: 3em;
  margin: 0px;
  padding: 0.375rem 0.7rem;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-logicContainer button.dtsb-clearGroup {
  border: none;
  border-radius: 0px;
  width: 2em;
  margin: 0px;
}

div.dt-button-collection div.dtsb-searchBuilder {
  padding-left: 10px;
  padding-right: 10px;
}


div.dtsp-topRow {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  border: 2px solid rgba(0, 0, 0, 0);
  border-radius: 3px;
  justify-content: space-around;
  align-content: flex-start;
  align-items: flex-start;
  min-height: 37px;
}
div.dtsp-topRow input.dtsp-search {
  text-overflow: ellipsis;
  min-width: 50px;
  flex-basis: 90px;
  max-width: none;
}
div.dtsp-topRow input.dtsp-search::placeholder {
  color: inherit;
}
div.dtsp-topRow div.dtsp-subRow1 {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  flex: 1 1 auto;
}
div.dtsp-topRow div.dtsp-subRow1 div.dtsp-searchCont {
  position: relative;
  width: 100%;
}
div.dtsp-topRow div.dtsp-subRow1 div.dtsp-searchCont input.dtsp-disabledButton {
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: transparent;
}
div.dtsp-topRow div.dtsp-subRow1 input {
  padding-right: 2em;
  width: 100% !important;
  box-sizing: border-box;
  font-size: 1em;
}
div.dtsp-topRow div.dtsp-subRow1 button.dtsp-searchIcon {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
}
div.dtsp-topRow div.dtsp-subRow1 button.dtsp-searchIcon span {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAABbmlDQ1BpY2MAACiRdZE7SwNBFIU/EyWikRRaiFhsoWKhEBREO42FTZAQFYza7G5eQhKX3QQJtoKNhWAh2vgq/AfaCrYKgqAIIhb+Al+NhPVOEkiQZJbZ+3FmzmXmDHjCGTPrtAYhm8vb0bmQthxb0Xzv+PHRxRSabjrWTCQSpun4eaRF1YdR1av5voajM55wTGhpF54wLTsvPC0c3sxbineFe8y0Hhc+ER6x5YDCt0o3KvymOFXhL8X2YnQWPKqnlqpjo47NtJ0VHhYeyGYKZvU86ib+RG5pQWqfzH4coswRQsOgwDoZ8oxKzUlmjX3Bsm+eDfGY8rcoYosjRVq8I6IWpGtCalL0hHwZiir3/3k6yfGxSnd/CNpeXfdzEHz7UNpz3d9T1y2dgfcFrnM1/4bkNPkt+l5NGziGwDZc3tQ04wCudqD32dJtvSx5ZXqSSfi4gK4YdN9Dx2olq+o650+wuCVPdAeHRzAk+wNrfw8JaBFXEnV+AAAACXBIWXMAAA9hAAAPYQGoP6dpAAABMUlEQVQoU6XRr0vDQRjH8akoM4iIjqGoOIZ5oIjB5XWxajaYDGLSIhhNYjcPRDSJwbQNw+L+BNGgYYo/5pT5/shz8vDlBgMPXux7z3N3z+25VOofYyCyd4ZYCavI4gXPsRp9LqiDdrEMH+8wv8Vh8gBfWclFPOEUN3hAHjlMoRa7wTzBS5xgKLFglPkZLjDic6HyDsEMNvGR2Nxifoci3tEI+X770JU0XmPXIlax+LTPh83fFox1X6kxyzdjm9UcdXi9S+Vti6svfyNULhNR9TVsYNhW6Ff9KKCNR7/Zv6eeaQ+6+qcdpu9BqGlp1HFgud+FYdzzUcUExu0Q/cdzHGEFetIlXKPjK/sbqYoOftMiS+j9jzEJPd1Wt+5+kdR/9EM9ucIC5jCbyPc01Q32kfsBppYz3hYFcCwAAAAASUVORK5CYII=") !important;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 12px;
}
div.dtsp-topRow div.dtsp-subRow2 {
  white-space: nowrap;
  flex: 0 0 auto;
}
div.dtsp-topRow button > span {
  display: inline-block;
  height: 100%;
  width: 100%;
}
div.dtsp-topRow button.dtsp-nameButton span {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAYAAAAe2bNZAAABcGlDQ1BpY2MAACiRdZHNSwJBGMYftTDS8FCHkA57sOigIAXRMQzyYh3UIKvL7rirwe66zK6IdA26dBA6RF36OvQf1DXoWhAERRAR9B/0dQnZ3nEFJXSG2ffHs/O8zDwD+DM6M+yBJGCYDs+mU9JaYV0KviNMM4QoEjKzreXcUh59x88jfKI+JESv/vt6jlBRtRngGyKeYxZ3iBeIMzXHErxHPMbKcpH4hDjO6YDEt0JXPH4TXPL4SzDPZxcBv+gplbpY6WJW5gbxNHHM0KusfR5xk7BqruaoRmlNwEYWaaQgQUEVW9DhIEHVpMx6+5It3woq5GH0tVAHJ0cJZfLGSa1SV5WqRrpKU0dd5P4/T1ubnfG6h1PA4Kvrfk4CwX2g2XDd31PXbZ4BgRfg2uz4K5TT/DfpjY4WOwYiO8DlTUdTDoCrXWD82ZK53JICtPyaBnxcACMFYPQeGN7wsmr/x/kTkN+mJ7oDDo+AKdof2fwDCBRoDkL8UccAAAAJcEhZcwAAD2EAAA9hAag/p2kAAAK2SURBVFgJ7ZY9j41BFICvryCExrJBQ6HyEYVEIREaUZDQIRoR2ViJKCioxV+gkVXYTVZEQiEUhG2EQnxUCh0FKolY4ut5XnM2cyfva3Pt5m7EPcmzZ2bemTNnzjkzd1utnvQi0IvAfxiBy5z5FoxO89kPY+8mbMjtzs47RXs5/WVpbAG6bWExt5PuIibvhVkwmC+ck3eK9ln6/fAddFojYzBVuYSBpcnIEvRaqOw2RcaN18FPuJH0JvRUxbT3wWf4ltiKPgfVidWlbGZgPozDFfgAC+EA/K2EI4cwcAJ+gPaeQ+VQU2SOMMGcPgPl/m/V2p50rrbRsRgt9Iv5h6xtpP22Bz7Ce1C+gFFxfKzOmShcU+Qmyh2w3w8rIJfddHTck66EukL/xPhj+JM8rHNmFys0Pg4v0up3aFNlwR9NYyodd3OL/C64zpsymcTFcf6ElM4YzjAWKYrJkaq8kE/yUYNP4BoYvS1QRo+hNtF5xfkTUjoTheukSFFMjlTFm6PjceOca/SMpKfeCR1L6Uzk/y2WIkVhNFJlJAZhP+hYns7b9D3IPuhY5mYrIv8OrQJvR5NYyNaW4jsU8pSGNySiVx4o5tXq3JkoXE/mg5R/M8dGJCJpKhaDcjBRdbI/Rm8g69c122om33BHmj2CHoV5qa9jUXBraJ+G1fAVjIBO1klc87ro1K4JZ/K35SWW3TwcyDd6TecqnAEd8cGq2+w84xvBm1n3vS0izKkkwh5XNC/GmFPqqAtPF89AOScKuemaNzoTV1SD5dtSbmLf1/RV+tC0WTgcj6R7HEtrVGWaqu/lYDZ/2pvxQ/kIyw/gFByHC9AHw910hv1aUUumyd8yy0QfhmEkfiNod0Xusct68J1qc8Tdux0Z97Q+hsDb+AYGYEbF/4Guw2Q/qDPqZG/zXgT+3Qj8AtKnfWhFwmuAAAAAAElFTkSuQmCC") !important;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 23px;
  vertical-align: bottom;
}
div.dtsp-topRow button.dtsp-countButton span {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAABcGlDQ1BpY2MAACiRdZHNSwJBGMYftTDS8FCHkA57sOigIAXRMQzyYh3UIKvL7rirwe66zK6IdA26dBA6RF36OvQf1DXoWhAERRAR9B/0dQnZ3nEFJXSG2ffHs/O8zDwD+DM6M+yBJGCYDs+mU9JaYV0KviNMM4QoEjKzreXcUh59x88jfKI+JESv/vt6jlBRtRngGyKeYxZ3iBeIMzXHErxHPMbKcpH4hDjO6YDEt0JXPH4TXPL4SzDPZxcBv+gplbpY6WJW5gbxNHHM0KusfR5xk7BqruaoRmlNwEYWaaQgQUEVW9DhIEHVpMx6+5It3woq5GH0tVAHJ0cJZfLGSa1SV5WqRrpKU0dd5P4/T1ubnfG6h1PA4Kvrfk4CwX2g2XDd31PXbZ4BgRfg2uz4K5TT/DfpjY4WOwYiO8DlTUdTDoCrXWD82ZK53JICtPyaBnxcACMFYPQeGN7wsmr/x/kTkN+mJ7oDDo+AKdof2fwDCBRoDkL8UccAAAAJcEhZcwAAD2EAAA9hAag/p2kAAAG5SURBVEgN3VU9LwVBFF0fiYhofUSlEQkKhU7z/oBCQkIiGr9BgUbhVzy9BAnhFyjV/AYFiU5ICM7ZN+c5Zud5dm3lJmfmzrkz9+7cu3c3y/6jjOBSF8CxXS7FmTkbwqIJjDpJvTcmsJ4K3KPZUpyZsx0sxoB9J6mnAkyC7wGuuCFIipNtEcpcWExgXpOBc78vgj6N+QO4NVsjwdFM59tUIDxDrHMBOeIQ34C5ZDregXuAQm4YcI68nN9B3wr2PcwPAIPkN2EqtJH6b+QZm1ajjTx7BqwAr26Lb+C2Kvpbt0Mb2HAJ7NrGFGfmXO3DeA4UshDfQAVmH0gaUFg852TTTDvlxwBlCtxy9zXyBhQFaq0wMmIdRebrfgosA3zb2hKnqG0oqchp4QbuR8X0TjzABhbdOT8jnQ/atcgqpnfwOA7yqZyTU587ZkIGdesLTt2EkynOnbreMUUKMI/dA4B/QVOcO13CQh+5wWCgDwo/75u59odB/wjmfhbgvACcAOyZPHihMWAoIwxyCLgf1oxfgjzVbgBXSTzIN+f0pg6s5DkcesLMRpsBrgE2XO3CN64JFP7JtUeKHX4CKtRRXFZ+7dEAAAAASUVORK5CYII=") !important;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 18px;
  vertical-align: bottom;
}
div.dtsp-topRow button.dtsp-collapseButton span.dtsp-caret {
  position: relative;
  top: 9px;
  display: inline-block;
}
div.dtsp-topRow button.dtsp-collapseButton.dtsp-rotated {
  transform: rotate(180deg);
}

div.dtsp-searchPane table thead th,
div.dtsp-searchPane table thead td {
  width: 100% !important;
}

div.dt-button-collection {
  z-index: 2002;
}

div.dt-button-collection.dtb-collection-closeable div.dtsp-titleRow {
  padding-right: 25px;
}

div.dtsp-columns-1 {
  max-width: 100%;
  min-width: 100%;
  margin: 0px !important;
}

div.dtsp-columns-2 {
  max-width: 49%;
  min-width: 49%;
  margin: 0px !important;
}

div.dtsp-columns-3 {
  max-width: 32%;
  min-width: 32%;
  margin: 0px !important;
}

div.dtsp-columns-4 {
  max-width: 24%;
  min-width: 24%;
  margin: 0px !important;
}

div.dtsp-columns-5 {
  max-width: 19%;
  min-width: 19%;
  margin: 0px !important;
}

div.dtsp-columns-6 {
  max-width: 16%;
  min-width: 16%;
  margin: 0px !important;
}

div.dtsp-columns-7 {
  max-width: 14%;
  min-width: 14%;
  margin: 0px !important;
}

div.dtsp-columns-8 {
  max-width: 12%;
  min-width: 12%;
  margin: 0px !important;
}

div.dtsp-columns-9 {
  max-width: 10.5%;
  min-width: 10.5%;
  margin: 0px !important;
}

div.dtsp-narrow {
  flex-direction: column !important;
}
div.dtsp-narrow div.dtsp-subRow1,
div.dtsp-narrow div.dtsp-subRow2 {
  width: 100%;
}
div.dtsp-narrow div.dtsp-subRow2 button {
  margin: 0 !important;
  width: 25% !important;
}

div.dt-button-collection {
  float: none;
}

div.dtsp-panesContainer {
  margin-bottom: 1em;
}

div.dtsp-searchPane div.dataTables_wrapper {
  width: 100%;
}
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_layout_cell {
  padding: 0;
}
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollHead {
  display: none !important;
}
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody {
  background: white !important;
  border-bottom: none;
}
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody thead {
  display: none;
}
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody table {
  table-layout: fixed;
}
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody table tr > th,
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody table tr > td {
  padding: 5px 10px;
}
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody td.dtsp-nameColumn {
  width: 100% !important;
}
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody div.dtsp-nameCont {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start;
}
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody div.dtsp-nameCont span.dtsp-name,
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody div.dtsp-nameCont span.dtsp-pill {
  cursor: default;
}
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody div.dtsp-nameCont span.dtsp-name {
  text-overflow: ellipsis;
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;
  white-space: nowrap;
  flex-grow: 1;
  text-align: left;
}
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody div.dtsp-nameCont span.dtsp-pill {
  display: inline-block;
  background-color: #cfcfcf;
  text-align: center;
  border-radius: 10px;
  width: auto;
  min-width: 30px;
  color: black;
  font-size: 0.9em;
  padding: 0 4px;
}
div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody div.dtsp-nameCont span.dtsp-pill:empty {
  display: none;
}

div.dtsp-panesContainer {
  clear: both;
  padding-left: 0;
  padding-right: 0;
  text-align: center;
}
div.dtsp-panesContainer div.dtsp-searchPanes {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: flex-start;
  align-items: stretch;
  clear: both;
  text-align: left;
}
div.dtsp-panesContainer div.dtsp-searchPanes div.dtsp-searchPane {
  flex-grow: 1;
  flex-shrink: 0;
  font-size: 0.9em;
  margin-top: 15px !important;
}
div.dtsp-panesContainer div.dtsp-searchPanes div.dtsp-searchPane div.dataTables_wrapper {
  flex: 1;
  box-sizing: border-box;
}
div.dtsp-panesContainer div.dtsp-searchPanes div.dtsp-searchPane div.dataTables_wrapper div.dataTables_filter {
  display: none;
}
div.dtsp-panesContainer div.dtsp-title {
  float: left;
  padding: 10px 0;
}
div.dtsp-panesContainer button.dtsp-clearAll,
div.dtsp-panesContainer button.dtsp-collapseAll,
div.dtsp-panesContainer button.dtsp-showAll {
  float: right;
  padding: 10px;
}

div.dtsp-hidden {
  display: none !important;
}

div.dtsp-panesContainer button.btn-subtle {
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
}
div.dtsp-panesContainer button.btn-subtle.disabled {
  opacity: 0.5;
}
div.dtsp-panesContainer button.btn-subtle:hover {
  background-color: #cbd3da;
}
div.dtsp-panesContainer button.dtsp-clearAll,
div.dtsp-panesContainer button.dtsp-showAll {
  margin-left: 3px;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow {
  margin: 0.5em 0;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow div.dtsp-subRow2 {
  margin-left: 0.5em;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow button {
  width: 35px;
  line-height: 20px;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow button.dtsp-searchIcon, div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow button.dtsp-nameButton, div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow button.dtsp-countButton {
  padding: 0;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow div.dtsp-subRow1 button {
  border-right: none;
  margin-right: 1px;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow div.dtsp-subRow1 input {
  padding-right: 3em;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow span.dtsp-caret {
  top: 3px;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow button.dtsp-rotated {
  transform: none;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow button.dtsp-rotated span {
  transform: rotate(180deg);
  top: -2px;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow.dtsp-bordered:hover button.disabled {
  cursor: pointer !important;
  pointer-events: none;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dtsp-topRow.dtsp-bordered:hover input.dtsp-paneInputButton {
  pointer-events: none;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dataTables_wrapper {
  border: 2px #f0f0f0 solid;
  border-radius: 4px;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dataTables_wrapper:hover {
  border: 2px solid #cfcfcf !important;
}
div.dtsp-panesContainer div.dtsp-searchPane div.dataTables_wrapper div.dtsp-nameCont span.badge {
  min-width: 30px;
  line-height: 1.25em;
  margin-top: 3.5px;
}
div.dtsp-panesContainer button.disabled {
  cursor: not-allowed;
}

div.dt-button-collection div.dtsp-panesContainer {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

html[data-bs-theme=dark] div.dtsp-topRow button.dtsp-searchIcon span {
  filter: invert();
}
html[data-bs-theme=dark] div.dtsp-topRow button.dtsp-nameButton span {
  filter: invert();
}
html[data-bs-theme=dark] div.dtsp-topRow button.dtsp-countButton span {
  filter: invert();
}
html[data-bs-theme=dark] div.dtsp-topRow input.dtsp-paneInputButton,
html[data-bs-theme=dark] div.dtsp-topRow button {
  color: inherit;
}
html[data-bs-theme=dark] div.dtsp-panesContainer button.btn-subtle {
  background-color: rgb(33, 37, 41);
  border: var(--bs-border-width) solid var(--bs-border-color);
}
html[data-bs-theme=dark] div.dtsp-panesContainer button.btn-subtle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
html[data-bs-theme=dark] div.dtsp-panesContainer button.dtsp-clearAll,
html[data-bs-theme=dark] div.dtsp-panesContainer button.dtsp-collapseAll,
html[data-bs-theme=dark] div.dtsp-panesContainer button.dtsp-showAll {
  color: inherit;
}
html[data-bs-theme=dark] div.dtsp-panesContainer button.dtsp-clearAll:hover,
html[data-bs-theme=dark] div.dtsp-panesContainer button.dtsp-collapseAll:hover,
html[data-bs-theme=dark] div.dtsp-panesContainer button.dtsp-showAll:hover {
  background-color: rgb(64, 69, 73);
}
html[data-bs-theme=dark] div.dtsp-panesContainer button.dtsp-disabledButton {
  color: rgb(124, 124, 124);
}
html[data-bs-theme=dark] div.dtsp-panesContainer div.dtsp-searchPane div.dataTables_wrapper {
  border: 1px solid rgba(255, 255, 255, 0.2);
}
html[data-bs-theme=dark] div.dtsp-panesContainer div.dtsp-searchPane div.dataTables_wrapper:hover {
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}
html[data-bs-theme=dark] div.dtsp-panesContainer div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody {
  background: rgb(47, 52, 56) !important;
}
html[data-bs-theme=dark] div.dtsp-panesContainer div.dtsp-searchPane div.dataTables_wrapper div.dataTables_scrollBody div.dtsp-nameCont span.dtsp-pill {
  background-color: rgb(33, 37, 41);
  color: inherit;
}


table.dataTable > tbody > tr > .selected {
  background-color: rgba(13, 110, 253, 0.9);
  color: white;
}
table.dataTable > tbody > tr > td.select-checkbox,
table.dataTable > tbody > tr > th.select-checkbox {
  position: relative;
}
table.dataTable > tbody > tr > td.select-checkbox:before, table.dataTable > tbody > tr > td.select-checkbox:after,
table.dataTable > tbody > tr > th.select-checkbox:before,
table.dataTable > tbody > tr > th.select-checkbox:after {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  box-sizing: border-box;
}
table.dataTable > tbody > tr > td.select-checkbox:before,
table.dataTable > tbody > tr > th.select-checkbox:before {
  content: " ";
  margin-top: -6px;
  margin-left: -6px;
  border: 1px solid black;
  border-radius: 3px;
}
table.dataTable > tbody > tr.selected > td.select-checkbox:before,
table.dataTable > tbody > tr.selected > th.select-checkbox:before {
  border: 1px solid white;
}
table.dataTable > tbody > tr.selected > td.select-checkbox:after,
table.dataTable > tbody > tr.selected > th.select-checkbox:after {
  content: "✓";
  font-size: 20px;
  margin-top: -12px;
  margin-left: -6px;
  text-align: center;
}
table.dataTable.compact > tbody > tr > td.select-checkbox:before,
table.dataTable.compact > tbody > tr > th.select-checkbox:before {
  margin-top: -12px;
}
table.dataTable.compact > tbody > tr.selected > td.select-checkbox:after,
table.dataTable.compact > tbody > tr.selected > th.select-checkbox:after {
  margin-top: -16px;
}

div.dataTables_wrapper span.select-info,
div.dataTables_wrapper span.select-item {
  margin-left: 0.5em;
}

html.dark table.dataTable > tbody > tr > td.select-checkbox:before,
html.dark table.dataTable > tbody > tr > th.select-checkbox:before,
html[data-bs-theme=dark] table.dataTable > tbody > tr > td.select-checkbox:before,
html[data-bs-theme=dark] table.dataTable > tbody > tr > th.select-checkbox:before {
  border: 1px solid rgba(255, 255, 255, 0.6);
}

@media screen and (max-width: 640px) {
  div.dataTables_wrapper span.select-info,
  div.dataTables_wrapper span.select-item {
    margin-left: 0;
    display: block;
  }
}
table.dataTable.table-sm tbody td.select-checkbox::before {
  margin-top: -9px;
}


div.dtsr-confirmation,
div.dtsr-creation {
  position: fixed;
  top: 20%;
  left: 50%;
  width: 500px;
  margin-left: -250px;
  background-color: white;
  border-radius: 0.75em;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.6);
  z-index: 2003;
  box-sizing: border-box;
  padding: 2em;
}
div.dtsr-confirmation button,
div.dtsr-creation button {
  display: inline-block;
  margin: 1em;
  padding: 0.5em 1em;
  border-radius: 0.5em;
  border: 1px solid rgba(0, 0, 0, 0.175);
  background-color: #f6f6f6;
  text-align: left;
  cursor: pointer;
}
div.dtsr-confirmation button:hover,
div.dtsr-creation button:hover {
  background-color: #ebebeb;
}
div.dtsr-confirmation div.dtsr-creation-text,
div.dtsr-confirmation div.dtsr-confirmation-title-row,
div.dtsr-creation div.dtsr-creation-text,
div.dtsr-creation div.dtsr-confirmation-title-row {
  text-align: left;
}
div.dtsr-confirmation div.dtsr-creation-text h2,
div.dtsr-confirmation div.dtsr-confirmation-title-row h2,
div.dtsr-creation div.dtsr-creation-text h2,
div.dtsr-creation div.dtsr-confirmation-title-row h2 {
  border-bottom: 0px;
  margin-top: 0px;
  padding-top: 0px;
  margin-bottom: 1rem;
}
div.dtsr-confirmation div.dtsr-confirmation-text,
div.dtsr-creation div.dtsr-confirmation-text {
  text-align: center;
}
div.dtsr-confirmation div.dtsr-modal-foot,
div.dtsr-confirmation div.dtsr-confirmation-buttons,
div.dtsr-creation div.dtsr-modal-foot,
div.dtsr-creation div.dtsr-confirmation-buttons {
  text-align: right;
  margin-top: 1em;
}
div.dtsr-confirmation div.dtsr-modal-foot button,
div.dtsr-confirmation div.dtsr-confirmation-buttons button,
div.dtsr-creation div.dtsr-modal-foot button,
div.dtsr-creation div.dtsr-confirmation-buttons button {
  margin: 0px;
}
div.dtsr-confirmation div.dtsr-creation-text,
div.dtsr-creation div.dtsr-creation-text {
  text-align: left;
  padding: 0px;
  border: none;
}
div.dtsr-confirmation div.dtsr-creation-text span,
div.dtsr-creation div.dtsr-creation-text span {
  font-size: 20px;
}
div.dtsr-confirmation div.dtsr-creation-form div.dtsr-left, div.dtsr-confirmation div.dtsr-creation-form div.dtsr-right,
div.dtsr-creation div.dtsr-creation-form div.dtsr-left,
div.dtsr-creation div.dtsr-creation-form div.dtsr-right {
  display: inline-block;
  width: 50%;
}
div.dtsr-confirmation div.dtsr-creation-form div.dtsr-left,
div.dtsr-creation div.dtsr-creation-form div.dtsr-left {
  text-align: right;
}
div.dtsr-confirmation div.dtsr-creation-form div.dtsr-right, div.dtsr-confirmation div.dtsr-creation-form div.dtsr-name-row,
div.dtsr-creation div.dtsr-creation-form div.dtsr-right,
div.dtsr-creation div.dtsr-creation-form div.dtsr-name-row {
  text-align: left;
}
div.dtsr-confirmation div.dtsr-creation-form div.dtsr-form-row,
div.dtsr-creation div.dtsr-creation-form div.dtsr-form-row {
  margin: 0.5em 0;
}
div.dtsr-confirmation div.dtsr-creation-form div.dtsr-form-row:first-child,
div.dtsr-creation div.dtsr-creation-form div.dtsr-form-row:first-child {
  margin-top: 0;
}
div.dtsr-confirmation div.dtsr-creation-form div.dtsr-form-row:last-child,
div.dtsr-creation div.dtsr-creation-form div.dtsr-form-row:last-child {
  margin-bottom: 0;
}
div.dtsr-confirmation div.dtsr-creation-form div.dtsr-form-row label.dtsr-name-label,
div.dtsr-creation div.dtsr-creation-form div.dtsr-form-row label.dtsr-name-label {
  width: 25%;
  display: inline-block;
  text-align: right;
  padding-right: 15px;
  padding-left: 15px;
}
div.dtsr-confirmation div.dtsr-creation-form div.dtsr-form-row input.dtsr-name-input,
div.dtsr-creation div.dtsr-creation-form div.dtsr-form-row input.dtsr-name-input {
  width: 66.6%;
  display: inline-block;
}
div.dtsr-confirmation div.dtsr-creation-form div.dtsr-form-row input.dtsr-check-box,
div.dtsr-creation div.dtsr-creation-form div.dtsr-form-row input.dtsr-check-box {
  margin-left: 25%;
  margin-right: 14px;
  display: inline-block;
}
div.dtsr-confirmation div.dtsr-confirmation-text,
div.dtsr-creation div.dtsr-confirmation-text {
  text-align: left;
}
div.dtsr-confirmation div.dtsr-confirmation-text label.dtsr-name-label,
div.dtsr-creation div.dtsr-confirmation-text label.dtsr-name-label {
  width: auto;
  display: inline-block;
  text-align: right;
  padding-right: 15px;
}
div.dtsr-confirmation div.dtsr-confirmation-text input.dtsr-name-input,
div.dtsr-creation div.dtsr-confirmation-text input.dtsr-name-input {
  width: 66.6%;
  display: inline-block;
}
div.dtsr-confirmation div.dtsr-confirmation-text input.dtsr-check-box,
div.dtsr-creation div.dtsr-confirmation-text input.dtsr-check-box {
  margin-left: 33.3%;
  margin-right: 14px;
  display: inline-block;
}
div.dtsr-confirmation div.dtsr-modal-foot,
div.dtsr-creation div.dtsr-modal-foot {
  text-align: right;
  padding-top: 10px;
}
div.dtsr-confirmation span.dtsr-modal-error,
div.dtsr-creation span.dtsr-modal-error {
  color: red;
  font-size: 0.9em;
}

div.dtsr-creation {
  top: 10%;
}

div.dtsr-check-row {
  padding-top: 0px;
}

div.dtsr-popover-close {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 22px;
  height: 22px;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
  z-index: 12;
}

div.dtsr-background {
  z-index: 2002;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  background: radial-gradient(ellipse farthest-corner at center, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
}

div.dt-button-collection h3 {
  text-align: center;
  margin-top: 4px;
  margin-bottom: 8px;
  font-size: 1.5em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
div.dt-button-collection span.dtsr-emptyStates {
  border-radius: 5px;
  display: inline-block;
  line-height: 1.6em;
  white-space: nowrap;
  text-align: center;
  vertical-align: middle;
  width: 100%;
  padding-bottom: 7px;
  padding-top: 3px;
}

html.dark div.dtsr-confirmation,
html.dark div.dtsr-creation,
html[data-bs-theme=dark] div.dtsr-confirmation,
html[data-bs-theme=dark] div.dtsr-creation {
  background-color: rgb(33, 37, 41);
  border: 1px solid rgba(255, 255, 255, 0.15);
}
html.dark div.dtsr-confirmation button,
html.dark div.dtsr-creation button,
html[data-bs-theme=dark] div.dtsr-confirmation button,
html[data-bs-theme=dark] div.dtsr-creation button {
  color: inherit;
  border: 1px solid rgba(255, 255, 255, 0.175);
  background-color: rgb(47, 52, 56);
}
html.dark div.dtsr-confirmation button:hover,
html.dark div.dtsr-creation button:hover,
html[data-bs-theme=dark] div.dtsr-confirmation button:hover,
html[data-bs-theme=dark] div.dtsr-creation button:hover {
  background-color: rgb(64, 69, 73);
}

span.dtsr-check-label {
  padding-left: 3px;
}

div.dt-button-collection h3 {
  margin-top: 8px;
}
div.dt-button-collection div.dropdown-menu {
  padding-top: 0px;
  padding-bottom: 4px;
}
div.dt-button-collection div.dropdown-menu div.dt-btn-split-wrapper {
  margin-top: 4px;
  padding-left: 3px;
  padding-right: 3px;
}
div.dt-button-collection div.dropdown-menu div.dt-btn-split-wrapper button.btn {
  width: calc(100% - 30px);
}
div.dt-button-collection div.dropdown-menu div.dt-btn-split-wrapper button.dt-btn-split-drop {
  width: 30px;
}
div.dt-button-collection div.dropdown-menu button.dt-btn-split-drop-button {
  margin-left: -2px;
  margin-right: -2px;
}

div.dt-button-split-left div.dropdown-menu {
  padding-left: 4px;
  padding-right: 4px;
}
div.dt-button-split-left div.dropdown-menu button.dt-btn-split-drop-button {
  color: rgb(255, 255, 255);
  border-radius: 4px;
  background-color: rgb(107, 117, 125);
  margin-top: 4px;
}
div.dt-button-split-left div.dropdown-menu button.dt-btn-split-drop-button:hover {
  background-color: rgb(90, 98, 104);
}

input.dtsr-input {
  margin-left: 3px;
  display: inline-block;
  width: auto;
}

div.dtsr-creation-form div.dtsr-check-row {
  height: 25px;
}
div.dtsr-creation-form div.dtsr-form-row label.dtsr-name-label {
  width: 33.3%;
  display: inline-block;
  text-align: left !important;
  padding-right: 15px;
  padding-left: 15px;
}
div.dtsr-creation-form div.dtsr-form-row input.dtsr-name-input {
  width: 66.6%;
  display: inline-block;
}
div.dtsr-creation-form div.dtsr-form-row input.dtsr-check-box {
  margin-left: 33.3%;
  margin-right: 14px;
  display: inline-block;
  position: relative;
  float: none;
  line-height: normal;
}

div.dtsr-confirmation-text {
  margin-top: 1.5em;
}


