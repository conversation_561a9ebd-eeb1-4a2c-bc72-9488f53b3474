/*! Bootstrap integration for DataTables' AutoFill
 * ©2015 SpryMedia Ltd - datatables.net/license
 */
!function(n){var o,u;"function"==typeof define&&define.amd?define(["jquery","datatables.net-se","datatables.net-autofill"],function(e){return n(e,window,document)}):"object"==typeof exports?(o=require("jquery"),u=function(e,t){t.fn.dataTable||require("datatables.net-se")(e,t),t.fn.dataTable.AutoFill||require("datatables.net-autofill")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||o(e),u(e,t),n(t,0,e.document)}:(u(window,o),module.exports=n(o,window,window.document))):n(jQuery,window,document)}(function(e,t,n,o){"use strict";e=e.fn.dataTable;return e.AutoFill.classes.btn="ui button",e});