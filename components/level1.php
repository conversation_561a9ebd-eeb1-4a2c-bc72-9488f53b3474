<!DOCTYPE html>
<html lang="en">
<head>
    <link href="public/css/tailwindLocal.css" rel="stylesheet">
    <title>SPIC Tape Library</title>
    <style>
        .container {
            width: 100%;
            padding: 20px;
        }
        .table-container {
            width: 100%;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed; /* Ensures columns maintain fixed widths */
        }
        th, td {
            padding: 12px;
            text-align: left;
            border: 1px solid black;
            word-wrap: break-word; /* Ensures content wraps within cells */
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body class="bg-gray-100">
    <?php
    if (isset($_GET['message'])) {
        echo '<div class="bg-green-500 text-white p-4 rounded mb-4">' . htmlspecialchars($_GET['message']) . '</div>';
    }
    ?>
    <div class="container mx-auto p-4">
        <h1 class="text-3xl font-bold text-center mb-4">TAPE LIBRARY</h1>
        <marquee behavior="scroll" direction="left" class="text-lg text-gray-700">Welcome to Tape Library</marquee>

        <div class="table-container">
            <table class='bg-white shadow-md rounded-lg border'>
                <thead>
                    <tr class="bg-gray-200 text-left text-sm uppercase border-b">
                        <th>ID</th>
                        <th>CPF-Number</th>
                        <th>Name</th>
                        <th>Tape-ID</th>
                        <th>Datatype</th>
                        <th>Date</th>
                        <th>Area</th>
                        <th>Survey Name</th>
                        <th>Acquisition Year</th>
                        <th>Group</th>
                        <th>Activity</th>
                        <th>Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    include("database.php");

                    $sql = "SELECT id, cpf_number, username, tapeid, datatype, date, area, survey_name, acquisition_year, group_name, activity, remarks FROM tapedetails";
                    $result = $conn->query($sql);

                    if ($result->num_rows > 0) {
                        while($row = $result->fetch_assoc()) {
                            echo "<tr class='hover:bg-gray-100'>";
                            echo "<td class='py-2 px-4'>" . $row["id"] . "</td>";
                            echo "<td class='py-2 px-4'>" . $row["cpf_number"] . "</td>";
                            echo "<td class='py-2 px-4'>" . $row["username"] . "</td>";
                            echo "<td class='py-2 px-4'>" . $row["tapeid"] . "</td>";
                            echo "<td class='py-2 px-4'>" . $row["datatype"] . "</td>";
                            echo "<td class='py-2 px-4'>" . $row["date"] . "</td>";
                            echo "<td class='py-2 px-4'>" . $row["area"] . "</td>";
                            echo "<td class='py-2 px-4'>" . $row["survey_name"] . "</td>";
                        //    echo "<td class='py-2 px-4'>" . $row["project_name"] . "</td>";
                            echo "<td class='py-2 px-4'>" . $row["acquisition_year"] . "</td>";
                            echo "<td class='py-2 px-4'>" . $row["group_name"] . "</td>";
                            echo "<td class='py-2 px-4'>" . $row["activity"] . "</td>";
                            echo "<td class='py-2 px-4'>" . $row["remarks"] . "</td>";
                            echo "</tr>";
                        }
                    } else {
                        echo "<tr><td colspan='13' class='py-2 px-4 text-center'>No results</td></tr>";
                    }

                    $conn->close();
                    ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="flex justify-center my-4">
        <a href="index.php"><button type="button" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">Go Back</button></a>&nbsp;&nbsp;
        <a href="request.php"><button type="button" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">Raise a Request</button></a>
    </div>
</body>
</html>

