
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Tape Details</title>
    <link href="public/css/tailwindLocal.css" rel="stylesheet">
    <style>
        /* Additional CSS for label and input alignment */
        .form-group {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .form-group label {
            width: 160px; /* Adjust width as needed */
            margin-right: 1rem;
            text-align: right;
        }
        .form-group input,
        .form-group select,
        .form-group textarea {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #ccc;
            border-radius: 0.25rem;
        }
        .form-group.double {
            display: flex;
            align-items: center;
            gap: 1rem; /* Gap between label and input */
        }
        .form-group.double label {
            width: auto; /* Adjust label width as needed */
            margin-right: 0.5rem;
            text-align: left;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <div class="container mx-auto p-4 flex-grow">
        <h1 class="text-3xl font-bold text-center mb-4">Submit Tape Details</h1>
        <div class="bg-white shadow-md rounded-lg p-6 border border-black">
            <form method="post" action="submit-request.php" class="space-y-2">
                <div class="form-group double">
                    <label for="cpf_number" class="text-sm font-medium text-gray-700">CPF Number</label>
                    <input type="text" id="cpf_number" name="cpf_number" class="mt-1 block w-full p-2 border border-gray-300 rounded-md" required>
                </div>
                <div class="form-group double">
                    <label for="username" class="text-sm font-medium text-gray-700">Name</label>
                    <input type="text" id="username" name="username" class="mt-1 block w-full p-2 border border-gray-300 rounded-md" required>
                </div>
                <div class="form-group double">
                    <label for="tapeid" class="text-sm font-medium text-gray-700">Tape ID</label>
                    <input type="text" id="tapeid" name="tapeid" class="mt-1 block w-full p-2 border border-gray-300 rounded-md" required>
                </div>
                <div class="form-group double">
                    <label for="datatype" class="text-sm font-medium text-gray-700">Datatype</label>
                    <input type="text" id="datatype" name="datatype" class="mt-1 block w-full p-2 border border-gray-300 rounded-md" required>
                </div>
                <div class="form-group double">
                    <label for="date" class="text-sm font-medium text-gray-700">Date</label>
                    <input type="date" id="date" name="date" class="mt-1 block w-full p-2 border border-gray-300 rounded-md" required>
                </div>
                <div class="form-group double">
                    <label for="area" class="text-sm font-medium text-gray-700">Area</label>
                    <input type="text" id="area" name="area" class="mt-1 block w-full p-2 border border-gray-300 rounded-md" required>
                </div>
                <div class="form-group double">
                    <label for="survey_name" class="text-sm font-medium text-gray-700">Survey Name</label>
                    <input type="text" id="survey_name" name="survey_name" class="mt-1 block w-full p-2 border border-gray-300 rounded-md" required>
                </div>
                <div class="form-group double">
                    <label for="project_name" class="text-sm font-medium text-gray-700">Project Name</label>
                    <input type="text" id="project_name" name="project_name" class="mt-1 block w-full p-2 border border-gray-300 rounded-md" required>
                </div>
                <div class="form-group double">
                    <label for="acquisition_year" class="text-sm font-medium text-gray-700">Acquisition Year</label>
                    <input type="number" id="acquisition_year" name="acquisition_year" class="mt-1 block w-full p-2 border border-gray-300 rounded-md" required>
                </div>
                <div class="form-group double">
                    <label for="group" class="text-sm font-medium text-gray-700">Group Number</label>
                    <select id="group" name="group" class="mt-1 block w-full p-2 border border-gray-300 rounded-md" required>
                        <option value="Group 1">Group 1</option>
                        <option value="Group 2">Group 2</option>
                        <option value="Group 3">Group 3</option>
                        <option value="Group 4">Group 4</option>
                    </select>
                </div>
                <div class="form-group double">
                    <label for="activity" class="text-sm font-medium text-gray-700">Activity</label>
                    <select id="activity" name="activity" class="mt-1 block w-full p-2 border border-gray-300 rounded-md" required>
                        <option value="Sent to DGH">Sent to DGH</option>
                        <option value="Received from TDMS">Received from TDMS</option>
                    </select>
                </div>
                <div class="form-group double">
                    <label for="remarks" class="text-sm font-medium text-gray-700">Remarks</label>
                    <textarea id="remarks" name="remarks" rows="5" class="mt-1 block w-full p-2 border border-gray-300 rounded-md"></textarea>
                </div>
                <div class="button-container mt-8 flex justify-center space-x-4">
                    <a href="index.php" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">Go Back</a>
                    <button type="submit" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">Submit Request</button>
                    <a href="index.php" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">Exit Page</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>


