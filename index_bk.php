    <?php include("components/header.php"); ?>
<head>
    <title>Button Page</title>
    <link href="public/css/tailwindLocal.css" rel="stylesheet">
    <style>
        .carousel {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .carousel-inner {
            position: relative;
            overflow: hidden;
            height: 500px;
            width: 100%;
        }
        .carousel-item img {
            height: 500px;
            width: 100%;
            object-fit: cover; 
        }
        .carousel-item {
            display: none;
        }
        .carousel-item.active {
            display: block;
        }
        .carousel-controls {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            display: flex;
            justify-content: space-between;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .carousel:hover .carousel-controls {
            opacity: 1;
        }
        .carousel-control {
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
        }
        .button-container {
            text-align: center;
            margin-top: 20px;
        }
        .generate-report-container {
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body class="bg-gray-100">
<p>&nbsp</p>
    <h1 style="color:#990000;font-family:'Trebuchet MS',sans-serif;text-align:center;font-style:bold;font-size:30px;font-weight:bold;">WELCOME TO SPIC TAPE DATA MANAGEMENT SYSTEM</h1>
  
<p>&nbsp</p>
    <p style="color:#0000e6;font-family:brush script;font-size:16px;width:1800px;margin:0 auto;text-align: center;"><i>"SeisData Processing and Interpretation Centre at NBP Green Height, Mumbai is one of the Seismic Data Processing centre of ONGC.The Centre processes huge volume of Seismic data using state of the art Enterprise Software solution. The acquired Seismic Data were received from the Operational fields mostly the Western Off-Shore Basin through various Tape Cartridges(3590/3592/LTO etc) and these Cartridges were managed at SPIC, TDMS. This portal has been developed to ensure secure and efficient Tape management using TS3500(E06,E07 drives),TS4500(E08,60f drives) and LTO drives as well as to accurately record and track tape  shipments to various Centres and Locations."</i></p>
<p>&nbsp</p>
    <div class="carousel relative mt-4" style="max-width: 800px; margin: 0 auto;">
        <div class="carousel-inner relative overflow-hidden w-full">
            <div class="carousel-item active">
                <img src="public/images/img1.png" alt="Slide 1">
            </div>
            <div class="carousel-item">
                <img src="public/images/img2.png" alt="Slide 2">
            </div>
            <div class="carousel-item">
                <img src="public/images/img3.png" alt="Slide 3">
            </div>
        </div>
        <div class="carousel-controls">
            <button class="carousel-control" onclick="prevSlide()">&#9664;</button>
            <button class="carousel-control" onclick="nextSlide()">&#9654;</button>
        </div>
    </div>

    <div class="button-container mt-8 flex justify-center space-x-4">
        <a href="request.php"><button type="button" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">Raise a Request</button></a>
        <a href="tape.php"><button type="button" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">View Data</button></a>
        <button class="bg-blue-500 text-white font-semibold py-2 px-4 rounded" onclick="generateReport()">Generate Report</button>
    </div>
    <p>
        &nbsp
    </p>
    <!-- Generate Report Form -->
    <div class="generate-report-container mt-8 hidden" id="generateReportSection">
        <form action="generate_report.php" method="POST" class="bg-white p-4 rounded shadow-md max-w-md mx-auto">
            <div class="mb-4">
                <label for="startDate" class="block text-sm font-medium text-gray-700">Start Date:</label>
                <input type="date" id="startDate" name="startDate" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            </div>
            <div class="mb-4">
                <label for="endDate" class="block text-sm font-medium text-gray-700">End Date:</label>
                <input type="date" id="endDate" name="endDate" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            </div>
            <button type="submit" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">Submit</button>
        </form>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.carousel-item');

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.toggle('active', i === index);
            });
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(currentSlide);
        }

        // Auto slide change every 10 seconds
        setInterval(nextSlide, 10000);

        function generateReport() {
            document.getElementById('generateReportSection').classList.toggle('hidden');
        }
    </script>

    <?php include("components/footer.php"); ?>
</body>
<br><br>