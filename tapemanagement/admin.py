from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import CustomUser, TapeRequest, FileUpload


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    """Admin configuration for CustomUser"""
    list_display = ('cpf_number', 'username', 'email', 'is_staff', 'is_active', 'date_joined')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'date_joined')
    search_fields = ('cpf_number', 'username', 'email')
    ordering = ('-date_joined',)

    fieldsets = UserAdmin.fieldsets + (
        ('Additional Info', {'fields': ('cpf_number',)}),
    )
    add_fieldsets = UserAdmin.add_fieldsets + (
        ('Additional Info', {'fields': ('cpf_number',)}),
    )


@admin.register(TapeRequest)
class TapeRequestAdmin(admin.ModelAdmin):
    """Admin configuration for TapeRequest"""
    list_display = ('id', 'tape_id', 'username', 'survey_name', 'status', 'created_at', 'updated_at')
    list_filter = ('status', 'group', 'activity', 'created_at', 'updated_at')
    search_fields = ('tape_id', 'username', 'survey_name', 'area', 'cpf_number')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('User Information', {
            'fields': ('user', 'cpf_number', 'username')
        }),
        ('Request Details', {
            'fields': ('tape_id', 'datatype', 'date', 'area', 'survey_name')
        }),
        ('Additional Information', {
            'fields': ('acquisition_year', 'group', 'activity', 'remarks')
        }),
        ('Media Information', {
            'fields': (('input_media_type', 'input_media_subtype'), ('output_media_type', 'output_media_subtype'))
        }),
        ('Status & Tracking', {
            'fields': ('status', 'created_at', 'updated_at')
        }),
    )

    actions = ['approve_requests', 'reject_requests', 'complete_requests']

    def approve_requests(self, request, queryset):
        updated = queryset.update(status='approved')
        self.message_user(request, f'{updated} requests were approved.')
    approve_requests.short_description = "Approve selected requests"

    def reject_requests(self, request, queryset):
        updated = queryset.update(status='rejected')
        self.message_user(request, f'{updated} requests were rejected.')
    reject_requests.short_description = "Reject selected requests"

    def complete_requests(self, request, queryset):
        updated = queryset.update(status='completed')
        self.message_user(request, f'{updated} requests were marked as completed.')
    complete_requests.short_description = "Mark selected requests as completed"


@admin.register(FileUpload)
class FileUploadAdmin(admin.ModelAdmin):
    """Admin configuration for FileUpload"""
    list_display = ('filename', 'tape_request', 'uploaded_by', 'uploaded_at')
    list_filter = ('uploaded_at',)
    search_fields = ('filename', 'tape_request__tape_id', 'uploaded_by__username')
    ordering = ('-uploaded_at',)
    readonly_fields = ('uploaded_at',)
