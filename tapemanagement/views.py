from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from .models import TapeRequest
from .forms import TapeRequestForm, FileUploadForm


def home_view(request):
    """Home page view - redirects to login if not authenticated"""
    if request.user.is_authenticated:
        return redirect('dashboard')
    return render(request, 'tapemanagement/home.html')


def login_view(request):
    """Custom login view using CPF number"""
    if request.user.is_authenticated:
        return redirect('dashboard')

    if request.method == 'POST':
        cpf_number = request.POST.get('cpf_number')
        password = request.POST.get('password')

        # Authenticate using CPF number
        user = authenticate(request, username=cpf_number, password=password)

        if user is not None:
            login(request, user)
            return redirect('dashboard')
        else:
            messages.error(request, 'Invalid CPF number or password.')

    return render(request, 'tapemanagement/login.html')


def logout_view(request):
    """Logout view"""
    logout(request)
    return redirect('login')


@login_required
def dashboard_view(request):
    """Dashboard view with three main options"""
    # Get some statistics for the dashboard
    user_requests = TapeRequest.objects.filter(user=request.user)
    pending_count = user_requests.filter(status='pending').count()
    completed_count = user_requests.filter(status__in=['approved', 'completed']).count()

    context = {
        'pending_count': pending_count,
        'completed_count': completed_count,
        'total_requests': user_requests.count(),
    }
    return render(request, 'tapemanagement/dashboard.html', context)


@login_required
def request_form_view(request):
    """View for creating new tape requests"""
    if request.method == 'POST':
        form = TapeRequestForm(request.POST)
        if form.is_valid():
            tape_request = form.save(commit=False)
            tape_request.user = request.user
            tape_request.cpf_number = request.user.cpf_number
            tape_request.username = request.user.username
            tape_request.save()
            messages.success(request, 'Request submitted successfully!')
            return redirect('pending_requests')
    else:
        # Pre-populate form with user data
        initial_data = {
            'cpf_number': request.user.cpf_number,
            'username': request.user.username,
        }
        form = TapeRequestForm(initial=initial_data)

    return render(request, 'tapemanagement/request_form.html', {'form': form})


@login_required
def pending_requests_view(request):
    """View for displaying user's pending requests"""
    if request.user.is_superuser:
        # Admin can see all requests
        requests = TapeRequest.objects.all()
    else:
        # Regular users see only their requests
        requests = TapeRequest.objects.filter(user=request.user)

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        requests = requests.filter(status=status_filter)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        requests = requests.filter(
            Q(tape_id__icontains=search_query) |
            Q(survey_name__icontains=search_query) |
            Q(area__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(requests, 10)  # Show 10 requests per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'search_query': search_query,
    }
    return render(request, 'tapemanagement/pending_requests.html', context)


@login_required
def completed_requests_view(request):
    """View for displaying completed/approved requests"""
    if request.user.is_superuser:
        # Admin can see all completed requests
        requests = TapeRequest.objects.filter(status__in=['approved', 'completed'])
    else:
        # Regular users see only their completed requests
        requests = TapeRequest.objects.filter(
            user=request.user,
            status__in=['approved', 'completed']
        )

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        requests = requests.filter(
            Q(tape_id__icontains=search_query) |
            Q(survey_name__icontains=search_query) |
            Q(area__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(requests, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
    }
    return render(request, 'tapemanagement/completed_requests.html', context)


@login_required
def upload_file_view(request, request_id):
    """View for uploading files to a specific request"""
    tape_request = get_object_or_404(TapeRequest, id=request_id)

    # Check if user owns this request or is admin
    if not request.user.is_superuser and tape_request.user != request.user:
        messages.error(request, 'You can only upload files to your own requests.')
        return redirect('pending_requests')

    if request.method == 'POST':
        form = FileUploadForm(request.POST, request.FILES)
        if form.is_valid():
            file_upload = form.save(commit=False)
            file_upload.tape_request = tape_request
            file_upload.uploaded_by = request.user
            file_upload.filename = file_upload.file.name
            file_upload.save()
            messages.success(request, 'File uploaded successfully!')
            return redirect('pending_requests')
    else:
        form = FileUploadForm()

    context = {
        'form': form,
        'tape_request': tape_request,
    }
    return render(request, 'tapemanagement/upload_file.html', context)


@login_required
def update_status_view(request, request_id):
    """View for admin to update request status"""
    if not request.user.is_superuser:
        messages.error(request, 'You do not have permission to update request status.')
        return redirect('pending_requests')

    tape_request = get_object_or_404(TapeRequest, id=request_id)

    if request.method == 'POST':
        from .forms import StatusUpdateForm
        form = StatusUpdateForm(request.POST, instance=tape_request)
        if form.is_valid():
            form.save()
            messages.success(request, f'Request status updated to {tape_request.status}.')
            return redirect('pending_requests')

    return redirect('pending_requests')
