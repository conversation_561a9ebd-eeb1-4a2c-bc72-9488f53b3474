from django import forms
from .models import TapeRequest, FileUpload


class Tape<PERSON>equestForm(forms.ModelForm):
    """Form for creating tape requests"""
    
    class Meta:
        model = TapeRequest
        fields = [
            'cpf_number', 'username', 'tape_id', 'datatype', 'date',
            'area', 'survey_name', 'acquisition_year',
            'group', 'activity', 'input_media', 'output_media', 'remarks'
        ]
        widgets = {
            'cpf_number': forms.TextInput(attrs={
                'class': 'form-control',
                'readonly': True,
                'placeholder': 'CPF Number'
            }),
            'username': forms.TextInput(attrs={
                'class': 'form-control',
                'readonly': True,
                'placeholder': 'Name'
            }),
            'tape_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Tape ID'
            }),
            'datatype': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Data Type'
            }),
            'date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'area': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Area'
            }),
            'survey_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Survey Name'
            }),
            'acquisition_year': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Acquisition Year (e.g., 2024)'
            }),
            'group': forms.Select(attrs={
                'class': 'form-control'
            }),
            'activity': forms.Select(attrs={
                'class': 'form-control'
            }),
            'input_media': forms.Select(attrs={
                'class': 'form-control'
            }),
            'output_media': forms.Select(attrs={
                'class': 'form-control'
            }),
            'remarks': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Remarks (optional)'
            }),
        }


class FileUploadForm(forms.ModelForm):
    """Form for uploading files to tape requests"""
    
    class Meta:
        model = FileUpload
        fields = ['file']
        widgets = {
            'file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.txt,.pdf'
            })
        }
    
    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            # Check file extension
            if not file.name.lower().endswith(('.txt', '.pdf')):
                raise forms.ValidationError('Only .txt and .pdf files are allowed.')
            
            # Check file size (limit to 10MB)
            if file.size > 10 * 1024 * 1024:
                raise forms.ValidationError('File size cannot exceed 10MB.')
        
        return file


class StatusUpdateForm(forms.ModelForm):
    """Form for admin to update request status"""
    
    class Meta:
        model = TapeRequest
        fields = ['status']
        widgets = {
            'status': forms.Select(attrs={
                'class': 'form-control'
            })
        }
