{% extends 'tapemanagement/base.html' %}

{% block title %}Completed Requests - SPIC TDMS{% endblock %}

{% block extra_css %}
<style>
    /* Stable table layout */
    .table-container {
        min-height: 400px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .table {
        table-layout: auto;
        width: 100%;
        min-width: 1000px;
        margin-bottom: 0;
    }

    .table th, .table td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 12px 8px;
    }

    .table th:nth-child(1), .table td:nth-child(1) { width: 10%; }
    .table th:nth-child(2), .table td:nth-child(2) { width: 15%; }
    .table th:nth-child(3), .table td:nth-child(3) { width: 20%; }
    .table th:nth-child(4), .table td:nth-child(4) { width: 15%; }
    .table th:nth-child(5), .table td:nth-child(5) { width: 15%; }
    .table th:nth-child(6), .table td:nth-child(6) { width: 10%; }
    .table th:nth-child(7), .table td:nth-child(7) { width: 8%; }
    .table th:nth-child(8), .table td:nth-child(8) { width: 12%; }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-check-circle"></i> Completed Requests</h2>
</div>

<!-- Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <input type="text" 
                       class="form-control" 
                       name="search" 
                       placeholder="Search by Tape ID, Survey, or Area..."
                       value="{{ search_query|default:'' }}">
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>
            <div class="col-md-3">
                <a href="{% url 'completed_requests' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Requests Table -->
{% if page_obj %}
<div class="card">
    <div class="card-body">
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Request ID</th>
                        <th>Tape ID</th>
                        <th>Survey Name</th>
                        <th>Area</th>
                        <th>Media Info</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Completed</th>
                    </tr>
                </thead>
                <tbody>
                    {% for request in page_obj %}
                    <tr>
                        <td><strong>#{{ request.id }}</strong></td>
                        <td>{{ request.tape_id }}</td>
                        <td>{{ request.survey_name }}</td>
                        <td>{{ request.area }}</td>
                        <td>
                            <small>
                                <strong class="text-success">In:</strong> {{ request.get_input_media_display }}<br>
                                <strong class="text-info">Out:</strong> {{ request.get_output_media_display }}
                            </small>
                        </td>
                        <td>{{ request.date|date:"M d, Y" }}</td>
                        <td>
                            <span class="badge bg-{% if request.status == 'approved' %}success{% else %}info{% endif %}">
                                {{ request.get_status_display }}
                            </span>
                        </td>
                        <td>{{ request.updated_at|date:"M d, Y" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
                </table>
            </div>
        </div>
        
        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Previous</a>
                    </li>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Next</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% else %}
<div class="card">
    <div class="card-body text-center">
        <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
        <h5>No completed requests found</h5>
        <p class="text-muted">You don't have any completed requests yet.</p>
    </div>
</div>
{% endif %}
{% endblock %}
