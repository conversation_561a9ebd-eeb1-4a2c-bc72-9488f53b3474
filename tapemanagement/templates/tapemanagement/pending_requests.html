{% extends 'tapemanagement/base.html' %}

{% block title %}Pending Requests - SPIC TDMS{% endblock %}

{% block extra_css %}
<style>
    /* Stable table layout */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .table {
        margin-bottom: 0;
        table-layout: auto;
        min-width: 1200px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-clock"></i> Pending Requests</h2>
    <a href="{% url 'request_form' %}" class="btn btn-primary">
        <i class="fas fa-plus"></i> New Request
    </a>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <input type="text" 
                       class="form-control" 
                       name="search" 
                       placeholder="Search by Tape ID, Survey, or Area..."
                       value="{{ search_query|default:'' }}">
            </div>
            <div class="col-md-3">
                <select name="status" class="form-control">
                    <option value="">All Status</option>
                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                    <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>Approved</option>
                    <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Rejected</option>
                    <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Completed</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>
            <div class="col-md-3">
                <a href="{% url 'pending_requests' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Requests Table -->
{% if page_obj %}
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>User Details</th>
                        <th>Tape Information</th>
                        <th>Survey Details</th>
                        <th>Technical Info</th>
                        <th>Media Info</th>
                        <th>Status</th>
                        <th>Files</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for request in page_obj %}
                    <tr>
                        <td>
                            <strong class="text-primary">#{{ request.id }}</strong>
                            <br>
                            <small class="text-muted">{{ request.created_at|date:"M d, Y" }}</small>
                        </td>
                        <td>
                            <div class="small">
                                <strong><i class="fas fa-user"></i> {{ request.username }}</strong><br>
                                <span class="text-muted"><i class="fas fa-id-card"></i> {{ request.cpf_number }}</span>
                            </div>
                        </td>
                        <td>
                            <div class="small">
                                <strong class="text-info"><i class="fas fa-tape"></i> {{ request.tape_id }}</strong><br>
                                <span class="text-muted">{{ request.datatype }}</span><br>
                                <span class="text-muted"><i class="fas fa-calendar"></i> {{ request.date|date:"M d, Y" }}</span>
                            </div>
                        </td>
                        <td>
                            <div class="small">
                                <strong><i class="fas fa-search"></i> {{ request.survey_name }}</strong><br>
                                <span class="text-muted"><i class="fas fa-map-marker-alt"></i> {{ request.area }}</span>
                            </div>
                        </td>
                        <td>
                            <div class="small">
                                <span class="badge bg-secondary">{{ request.acquisition_year }}</span><br>
                                <span class="text-muted"><i class="fas fa-users"></i> {{ request.group }}</span><br>
                                <span class="text-muted"><i class="fas fa-tasks"></i> {{ request.activity }}</span>
                            </div>
                        </td>
                        <td>
                            <div class="small">
                                <strong class="text-success"><i class="fas fa-arrow-right"></i> Input:</strong><br>
                                <span class="text-muted">{{ request.get_input_media_display }}</span><br>
                                {% if request.input_vendor_id %}
                                    <span class="text-muted"><i class="fas fa-barcode"></i> {{ request.input_vendor_id }}</span><br>
                                {% endif %}
                                <strong class="text-info"><i class="fas fa-arrow-left"></i> Output:</strong><br>
                                <span class="text-muted">{{ request.get_output_media_display }}</span><br>
                                {% if request.output_vendor_id %}
                                    <span class="text-muted"><i class="fas fa-barcode"></i> {{ request.output_vendor_id }}</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{% if request.status == 'pending' %}warning{% elif request.status == 'approved' %}success{% elif request.status == 'rejected' %}danger{% else %}info{% endif %}">
                                {{ request.get_status_display }}
                            </span>
                            <br>
                            <small class="text-muted">{{ request.updated_at|date:"M d" }}</small>
                        </td>
                        <td>
                            <div class="small">
                                {% if request.uploads.all %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-file"></i> {{ request.uploads.count }}
                                    </span>
                                    <br>
                                    {% for upload in request.uploads.all|slice:":2" %}
                                        <small class="text-muted d-block">
                                            <i class="fas fa-file-{% if upload.filename|slice:'-4:' == '.pdf' %}pdf{% else %}alt{% endif %}"></i>
                                            {{ upload.filename|truncatechars:15 }}
                                        </small>
                                    {% endfor %}
                                    {% if request.uploads.count > 2 %}
                                        <small class="text-muted">+{{ request.uploads.count|add:"-2" }} more</small>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-light text-dark">
                                        <i class="fas fa-upload"></i> No files
                                    </span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <!-- Upload File Button -->
                                {% if request.status == 'pending' or request.status == 'approved' %}
                                <a href="{% url 'upload_file' request.id %}"
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-upload"></i> Upload
                                </a>
                                {% endif %}

                                <!-- Admin Status Update -->
                                {% if user.is_superuser and request.status == 'pending' %}
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <form method="post" action="{% url 'update_status' request.id %}" style="display: inline;">
                                                {% csrf_token %}
                                                <input type="hidden" name="status" value="approved">
                                                <button type="submit" class="dropdown-item text-success">
                                                    <i class="fas fa-check"></i> Approve
                                                </button>
                                            </form>
                                        </li>
                                        <li>
                                            <form method="post" action="{% url 'update_status' request.id %}" style="display: inline;">
                                                {% csrf_token %}
                                                <input type="hidden" name="status" value="rejected">
                                                <button type="submit" class="dropdown-item text-danger">
                                                    <i class="fas fa-times"></i> Reject
                                                </button>
                                            </form>
                                        </li>
                                        <li>
                                            <form method="post" action="{% url 'update_status' request.id %}" style="display: inline;">
                                                {% csrf_token %}
                                                <input type="hidden" name="status" value="completed">
                                                <button type="submit" class="dropdown-item text-info">
                                                    <i class="fas fa-check-circle"></i> Complete
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                    </li>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% else %}
<div class="card">
    <div class="card-body text-center">
        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
        <h5>No requests found</h5>
        <p class="text-muted">You haven't submitted any requests yet.</p>
        <a href="{% url 'request_form' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Submit Your First Request
        </a>
    </div>
</div>
{% endif %}
{% endblock %}
