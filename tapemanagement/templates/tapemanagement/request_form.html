{% extends 'tapemanagement/base.html' %}

{% block title %}New Request - SPIC TDMS{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-plus-circle"></i> Submit New Tape Request</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.cpf_number.id_for_label }}" class="form-label">
                                    <i class="fas fa-id-card"></i> CPF Number
                                </label>
                                {{ form.cpf_number }}
                                {% if form.cpf_number.errors %}
                                    <div class="text-danger">{{ form.cpf_number.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    <i class="fas fa-user"></i> Name
                                </label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger">{{ form.username.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.tape_id.id_for_label }}" class="form-label">
                                    <i class="fas fa-tape"></i> Tape ID *
                                </label>
                                {{ form.tape_id }}
                                {% if form.tape_id.errors %}
                                    <div class="text-danger">{{ form.tape_id.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.datatype.id_for_label }}" class="form-label">
                                    <i class="fas fa-database"></i> Data Type *
                                </label>
                                {{ form.datatype }}
                                {% if form.datatype.errors %}
                                    <div class="text-danger">{{ form.datatype.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar"></i> Date *
                                </label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger">{{ form.date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.area.id_for_label }}" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i> Area *
                                </label>
                                {{ form.area }}
                                {% if form.area.errors %}
                                    <div class="text-danger">{{ form.area.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="{{ form.survey_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-search"></i> Survey Name *
                                </label>
                                {{ form.survey_name }}
                                {% if form.survey_name.errors %}
                                    <div class="text-danger">{{ form.survey_name.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.acquisition_year.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar-alt"></i> Acquisition Year *
                                </label>
                                {{ form.acquisition_year }}
                                {% if form.acquisition_year.errors %}
                                    <div class="text-danger">{{ form.acquisition_year.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.group.id_for_label }}" class="form-label">
                                    <i class="fas fa-users"></i> Group *
                                </label>
                                {{ form.group }}
                                {% if form.group.errors %}
                                    <div class="text-danger">{{ form.group.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.activity.id_for_label }}" class="form-label">
                                    <i class="fas fa-tasks"></i> Activity *
                                </label>
                                {{ form.activity }}
                                {% if form.activity.errors %}
                                    <div class="text-danger">{{ form.activity.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.input_media.id_for_label }}" class="form-label">
                                    <i class="fas fa-hdd"></i> Input Media *
                                </label>
                                {{ form.input_media }}
                                {% if form.input_media.errors %}
                                    <div class="text-danger">{{ form.input_media.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.output_media.id_for_label }}" class="form-label">
                                    <i class="fas fa-save"></i> Output Media *
                                </label>
                                {{ form.output_media }}
                                {% if form.output_media.errors %}
                                    <div class="text-danger">{{ form.output_media.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.remarks.id_for_label }}" class="form-label">
                            <i class="fas fa-comment"></i> Remarks
                        </label>
                        {{ form.remarks }}
                        {% if form.remarks.errors %}
                            <div class="text-danger">{{ form.remarks.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'dashboard' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> Submit Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle"></i> Important Notes:</h6>
            <ul class="mb-0">
                <li>All fields marked with (*) are required</li>
                <li>After submission, you can upload supporting documents in the Pending section</li>
                <li>Request status will be updated by administrators</li>
                <li>You will receive notifications about status changes</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}
