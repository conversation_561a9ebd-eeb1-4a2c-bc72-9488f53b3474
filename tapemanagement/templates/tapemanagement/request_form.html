{% extends 'tapemanagement/base.html' %}

{% block title %}New Request - SPIC TDMS{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-plus-circle"></i> Submit New Tape Request</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.cpf_number.id_for_label }}" class="form-label">
                                    <i class="fas fa-id-card"></i> CPF Number
                                </label>
                                {{ form.cpf_number }}
                                {% if form.cpf_number.errors %}
                                    <div class="text-danger">{{ form.cpf_number.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    <i class="fas fa-user"></i> Name
                                </label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger">{{ form.username.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.tape_id.id_for_label }}" class="form-label">
                                    <i class="fas fa-tape"></i> Tape ID *
                                </label>
                                {{ form.tape_id }}
                                {% if form.tape_id.errors %}
                                    <div class="text-danger">{{ form.tape_id.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.datatype.id_for_label }}" class="form-label">
                                    <i class="fas fa-database"></i> Data Type *
                                </label>
                                {{ form.datatype }}
                                {% if form.datatype.errors %}
                                    <div class="text-danger">{{ form.datatype.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.date.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar"></i> Date *
                                </label>
                                {{ form.date }}
                                {% if form.date.errors %}
                                    <div class="text-danger">{{ form.date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.area.id_for_label }}" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i> Area *
                                </label>
                                {{ form.area }}
                                {% if form.area.errors %}
                                    <div class="text-danger">{{ form.area.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="{{ form.survey_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-search"></i> Survey Name *
                                </label>
                                {{ form.survey_name }}
                                {% if form.survey_name.errors %}
                                    <div class="text-danger">{{ form.survey_name.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.acquisition_year.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar-alt"></i> Acquisition Year *
                                </label>
                                {{ form.acquisition_year }}
                                {% if form.acquisition_year.errors %}
                                    <div class="text-danger">{{ form.acquisition_year.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.group.id_for_label }}" class="form-label">
                                    <i class="fas fa-users"></i> Group *
                                </label>
                                {{ form.group }}
                                {% if form.group.errors %}
                                    <div class="text-danger">{{ form.group.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.activity.id_for_label }}" class="form-label">
                                    <i class="fas fa-tasks"></i> Activity *
                                </label>
                                {{ form.activity }}
                                {% if form.activity.errors %}
                                    <div class="text-danger">{{ form.activity.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.input_media_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-hdd"></i> Input Media *
                                </label>
                                {{ form.input_media_type }}
                                {% if form.input_media_type.errors %}
                                    <div class="text-danger">{{ form.input_media_type.errors }}</div>
                                {% endif %}

                                <!-- Input Media Subtype - appears below main dropdown -->
                                <div id="input_subtype_container" style="display: none; margin-top: 10px;">
                                    <select name="input_media_subtype" class="form-control" id="id_input_media_subtype">
                                        <option value="">-- Select Option --</option>
                                    </select>
                                    {% if form.input_media_subtype.errors %}
                                        <div class="text-danger">{{ form.input_media_subtype.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.input_vendor_id.id_for_label }}" class="form-label">
                                    <i class="fas fa-barcode"></i> Input Vendor ID
                                </label>
                                {{ form.input_vendor_id }}
                                {% if form.input_vendor_id.errors %}
                                    <div class="text-danger">{{ form.input_vendor_id.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.output_media_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-save"></i> Output Media *
                                </label>
                                {{ form.output_media_type }}
                                {% if form.output_media_type.errors %}
                                    <div class="text-danger">{{ form.output_media_type.errors }}</div>
                                {% endif %}

                                <!-- Output Media Subtype - appears below main dropdown -->
                                <div id="output_subtype_container" style="display: none; margin-top: 10px;">
                                    <select name="output_media_subtype" class="form-control" id="id_output_media_subtype">
                                        <option value="">-- Select Option --</option>
                                    </select>
                                    {% if form.output_media_subtype.errors %}
                                        <div class="text-danger">{{ form.output_media_subtype.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.output_vendor_id.id_for_label }}" class="form-label">
                                    <i class="fas fa-barcode"></i> Output Vendor ID
                                </label>
                                {{ form.output_vendor_id }}
                                {% if form.output_vendor_id.errors %}
                                    <div class="text-danger">{{ form.output_vendor_id.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.remarks.id_for_label }}" class="form-label">
                            <i class="fas fa-comment"></i> Remarks
                        </label>
                        {{ form.remarks }}
                        {% if form.remarks.errors %}
                            <div class="text-danger">{{ form.remarks.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'dashboard' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> Submit Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle"></i> Important Notes:</h6>
            <ul class="mb-0">
                <li>All fields marked with (*) are required</li>
                <li>After submission, you can upload supporting documents in the Pending section</li>
                <li>Request status will be updated by administrators</li>
                <li>You will receive notifications about status changes</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Define subtype options for each media type
    const subtypeOptions = {
        '3592': [
            {value: 'JA', text: 'JA'},
            {value: 'JB', text: 'JB'},
            {value: 'JC', text: 'JC'},
            {value: 'JD', text: 'JD'},
            {value: 'JE', text: 'JE'}
        ],
        'LTO': [
            {value: '1', text: '1'},
            {value: '2', text: '2'},
            {value: '3', text: '3'},
            {value: '4', text: '4'},
            {value: '5', text: '5'},
            {value: '6', text: '6'},
            {value: '7', text: '7'},
            {value: '8', text: '8'},
            {value: '9', text: '9'}
        ],
        'DISK_STORAGE': [
            {value: 'SPIC_LFS', text: 'SPIC LFS'},
            {value: 'SPIC_GPFS', text: 'SPIC GPFS'}
        ],
        'HDD': [
            {value: '5TB', text: '5TB'},
            {value: '8TB', text: '8TB'}
        ]
    };

    // Function to update subtype dropdown
    function updateSubtypeDropdown(mediaTypeSelect, subtypeSelect, subtypeContainer) {
        const selectedType = mediaTypeSelect.value;
        console.log('Selected media type:', selectedType); // Debug log

        // Clear existing options
        subtypeSelect.innerHTML = '<option value="">-- Select Option --</option>';

        if (selectedType && subtypeOptions[selectedType]) {
            // Show subtype container
            subtypeContainer.style.display = 'block';
            console.log('Showing subtype container for:', selectedType); // Debug log

            // Add options for selected type
            subtypeOptions[selectedType].forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.text;
                subtypeSelect.appendChild(optionElement);
            });
        } else if (selectedType === 'CD') {
            // Hide subtype container for CD only (HDD now has subtypes)
            subtypeContainer.style.display = 'none';
            subtypeSelect.value = '';
        } else {
            // Hide subtype container if no type selected
            subtypeContainer.style.display = 'none';
        }
    }

    // Get form elements
    const inputMediaType = document.getElementById('id_input_media_type');
    const inputMediaSubtype = document.getElementById('id_input_media_subtype');
    const inputSubtypeContainer = document.getElementById('input_subtype_container');

    const outputMediaType = document.getElementById('id_output_media_type');
    const outputMediaSubtype = document.getElementById('id_output_media_subtype');
    const outputSubtypeContainer = document.getElementById('output_subtype_container');

    // Add event listeners for input media
    if (inputMediaType) {
        inputMediaType.addEventListener('change', function() {
            updateSubtypeDropdown(inputMediaType, inputMediaSubtype, inputSubtypeContainer);
        });

        // Initialize on page load if there's already a value
        if (inputMediaType.value) {
            updateSubtypeDropdown(inputMediaType, inputMediaSubtype, inputSubtypeContainer);
        }
    }

    // Add event listeners for output media
    if (outputMediaType) {
        outputMediaType.addEventListener('change', function() {
            updateSubtypeDropdown(outputMediaType, outputMediaSubtype, outputSubtypeContainer);
        });

        // Initialize on page load if there's already a value
        if (outputMediaType.value) {
            updateSubtypeDropdown(outputMediaType, outputMediaSubtype, outputSubtypeContainer);
        }
    }
});
</script>
{% endblock %}
