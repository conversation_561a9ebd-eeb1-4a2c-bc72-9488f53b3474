from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import FileExtensionValidator


class CustomUser(AbstractUser):
    """Custom user model with CPF number for authentication"""
    cpf_number = models.CharField(max_length=14, unique=True, help_text="CPF Number for login")

    USERNAME_FIELD = 'cpf_number'
    REQUIRED_FIELDS = ['username', 'email']

    def __str__(self):
        return f"{self.cpf_number} - {self.username}"


class TapeRequest(models.Model):
    """Model for tape library requests"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
    ]

    GROUP_CHOICES = [
        ('Group 1', 'Group 1'),
        ('Group 2', 'Group 2'),
        ('Group 3', 'Group 3'),
        ('Group 4', 'Group 4'),
    ]

    ACTIVITY_CHOICES = [
        ('Sent to DGH', 'Sent to DGH'),
        ('Received from TDMS', 'Received from TDMS'),
    ]

    # Media choices for input and output media
    MEDIA_CHOICES = [
        ('3592_JA', '3592 - JA'),
        ('3592_JB', '3592 - JB'),
        ('3592_JC', '3592 - JC'),
        ('3592_JD', '3592 - JD'),
        ('3592_JE', '3592 - JE'),
        ('LTO_1', 'LTO - 1'),
        ('LTO_2', 'LTO - 2'),
        ('LTO_3', 'LTO - 3'),
        ('LTO_4', 'LTO - 4'),
        ('LTO_5', 'LTO - 5'),
        ('LTO_6', 'LTO - 6'),
        ('LTO_7', 'LTO - 7'),
        ('LTO_8', 'LTO - 8'),
        ('LTO_9', 'LTO - 9'),
        ('DISK_SPIC_LFS', 'Disk Storage - SPIC LFS'),
        ('DISK_SPIC_GPFS', 'Disk Storage - SPIC GPFS'),
        ('HDD', 'HDD'),
        ('CD', 'CD'),
    ]

    # User who submitted the request
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='tape_requests')

    # Form fields from the original request.html
    cpf_number = models.CharField(max_length=14)
    username = models.CharField(max_length=100)
    tape_id = models.CharField(max_length=50)
    datatype = models.CharField(max_length=100)
    date = models.DateField()
    area = models.CharField(max_length=100)
    survey_name = models.CharField(max_length=100)
    project_name = models.CharField(max_length=100)
    acquisition_year = models.CharField(max_length=10)
    group = models.CharField(max_length=20, choices=GROUP_CHOICES)
    activity = models.CharField(max_length=50, choices=ACTIVITY_CHOICES)
    input_media = models.CharField(max_length=50, choices=MEDIA_CHOICES)
    output_media = models.CharField(max_length=50, choices=MEDIA_CHOICES)
    remarks = models.TextField(blank=True, null=True)

    # Status and tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Request {self.id} - {self.tape_id} by {self.username}"


class FileUpload(models.Model):
    """Model for file uploads associated with tape requests"""
    tape_request = models.ForeignKey(TapeRequest, on_delete=models.CASCADE, related_name='uploads')
    file = models.FileField(
        upload_to='uploads/%Y/%m/%d/',
        validators=[FileExtensionValidator(allowed_extensions=['txt', 'pdf'])]
    )
    filename = models.CharField(max_length=255)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE)

    def __str__(self):
        return f"File: {self.filename} for Request {self.tape_request.id}"
