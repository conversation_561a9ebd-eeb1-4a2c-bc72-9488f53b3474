from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import FileExtensionValidator


class CustomUser(AbstractUser):
    """Custom user model with CPF number for authentication"""
    cpf_number = models.CharField(max_length=14, unique=True, help_text="CPF Number for login")

    USERNAME_FIELD = 'cpf_number'
    REQUIRED_FIELDS = ['username', 'email']

    def __str__(self):
        return f"{self.cpf_number} - {self.username}"


class TapeRequest(models.Model):
    """Model for tape library requests"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
    ]

    GROUP_CHOICES = [
        ('Group 1', 'Group 1'),
        ('Group 2', 'Group 2'),
        ('Group 3', 'Group 3'),
        ('Group 4', 'Group 4'),
    ]

    ACTIVITY_CHOICES = [
        ('Sent to DGH', 'Sent to DGH'),
        ('Received from TDMS', 'Received from TDMS'),
    ]

    # User who submitted the request
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='tape_requests')

    # Form fields from the original request.html
    cpf_number = models.CharField(max_length=14)
    username = models.CharField(max_length=100)
    tape_id = models.CharField(max_length=50)
    datatype = models.CharField(max_length=100)
    date = models.DateField()
    area = models.CharField(max_length=100)
    survey_name = models.CharField(max_length=100)
    project_name = models.CharField(max_length=100)
    acquisition_year = models.CharField(max_length=10)
    group = models.CharField(max_length=20, choices=GROUP_CHOICES)
    activity = models.CharField(max_length=50, choices=ACTIVITY_CHOICES)
    remarks = models.TextField(blank=True, null=True)

    # Status and tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Request {self.id} - {self.tape_id} by {self.username}"


class FileUpload(models.Model):
    """Model for file uploads associated with tape requests"""
    tape_request = models.ForeignKey(TapeRequest, on_delete=models.CASCADE, related_name='uploads')
    file = models.FileField(
        upload_to='uploads/%Y/%m/%d/',
        validators=[FileExtensionValidator(allowed_extensions=['txt', 'pdf'])]
    )
    filename = models.CharField(max_length=255)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE)

    def __str__(self):
        return f"File: {self.filename} for Request {self.tape_request.id}"
