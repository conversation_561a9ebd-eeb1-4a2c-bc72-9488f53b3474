<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="../bootstrap/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="../datatables/datatables.min.css" rel="stylesheet" />

    <title>Seisdata Processing Center</title>
    <script>
        function redirect(){
            window.location.href="/tapelibrary/request.html"
        }
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0; /* Light gray background */
            background: linear-gradient(135deg, #f0f0f0 25%, #d0d0d0 100%);
        }
        .header {
            background-color: #03263E;
            color: #FFFFFF;
            padding: 20px;
            text-align: left;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .logo {
            width: 150px;
            margin-right: 20px;
        }
        .header h1 {
            font-size: 28px;
            margin: 0;
            color: #FFFFFF;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .button:hover {
            background-color: #0056b3;
        }
        .button {
  background-color: #04AA6D; /* Green */
  border: none;
  color: white;
  padding: 15px 32px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
}
        .container {
            padding: 40px;
            text-align: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            font-size: 36px;
            color: #03263E;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-align: center;
        }
        marquee {
            font-size: 20px;
            color: #34495e;
            margin-bottom: 20px; /* Adjusted margin */
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border: 1px solid #000; /* Black border for the table */
        }
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #000; /* Black border for rows */
            border-right: 1px solid #000; /* Black border for columns */
            color: #000; /* Black text color */
        }
        th {
            background-color: #f0f0f0; /* Light gray background for headers */
            font-weight: bold;
            text-transform: uppercase;
        }
        tr:hover {
            background-color: #f9f9f9; /* Light gray background on hover */
        }
        .footer {
            margin-top: 40px;
            font-size: 14px;
            color: #7f8c8d;
            text-align: center;
        }
        .footer a {
            color: #0366d6;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .btn-text-left{
	text-align: left;	
    
}
    </style>
</head>
<body>
    <header class="header">
        
        <img src="ongc_logo.png" alt="Logo" class="logo">
        <h1>Seisdata Processing Center</h1>
        
    </header>
    <div class="container">
        <h1>Seisdata Processing Center</h1>
        <marquee behavior="scroll" direction="left">Welcome to Tape Library</marquee>
        <div class="btn-text-left">
            <button type="button" class="button" onclick="redirect()">Raise a Request</button>
      </div>
        <table class='table table-striped table-bordered' id='results-table'>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>CPF-Number</th>
                    <th>Name</th>
                    <th>Tape-ID</th>
                    <th>Datatype</th>
                    <th>Date</th>
                    <th>Area</th>
                    <th>Survey Name</th>
                    <th>Project Name</th>
                    <th>Acquisition Year</th>
                    <th>Group</th>
                    <th>Activity</th>
                    <th>Remarks</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // Connect to your MySQL database
                
                $servername = "localhost"; // Replace with your MySQL server hostname
                $username = "root"; // Replace with your MySQL username
                $password = ""; // Replace with your MySQL password
                $dbname = "Tape_Management"; // Replace with your MySQL database name
                $conn="";
                // Create connection
                $conn = new mysqli($servername, $username, $password, $dbname);
                
                try{
    $conn =  new mysqli($servername, $username, $password, $dbname);
    }
   catch(Exception $e){
  echo $e->getMessage();
}

                

                // Fetch data from MySQL table
                $sql = "SELECT id,cpf_number,username,tapeid,datatype, date, area, survey_name, project_name, acquisition_year, group_name,activity,remarks FROM tapedetails";
                //$result = $conn->query($sql);
                 $result=mysqli_query($conn,$sql);
                // Output data of each row
                if ($result->num_rows > 0) {
                    while($row = mysqli_fetch_assoc($result)) {
                     // while($row = $result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . $row["id"] . "</td>";
                        echo "<td>" . $row["cpf_number"] . "</td>";
                        echo "<td>" . $row["username"] . "</td>";
                        echo "<td>" . $row["tapeid"] . "</td>";
                        echo "<td>" . $row["datatype"] . "</td>";
                        echo "<td>" . $row["date"] . "</td>";
                        echo "<td>" . $row["area"] . "</td>";
                        echo "<td>" . $row["survey_name"] . "</td>";
                        echo "<td>" . $row["project_name"] . "</td>";
                        echo "<td>" . $row["acquisition_year"] . "</td>";
                        echo "<td>" . $row["group_name"] . "</td>";
                        echo "<td>" . $row["activity"] . "</td>";
                        echo "<td>" . $row["remarks"] . "</td>";
                        echo "</tr>";
                    }
                } else {
                    echo "0 results";
                }

                // Close connection
                $conn->close();
                ?>
            </tbody>
        </table>
    </div>
    <script src="../jquery/dist/jquery.min.js" type="text/javascript"></script>
    <script src="../bootstrap/dist/js/bootstrap.bundle.min.js" type="text/javascript"></script>
    <script src="../datatables/datatables.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            $('#results-table').DataTable();
        });
     </script>
    <div class="footer">
        <p>For more information, visit our <a href="#">homepage</a> or <a href="#">contact us</a>.</p>
    </div>
</body>
</html>
