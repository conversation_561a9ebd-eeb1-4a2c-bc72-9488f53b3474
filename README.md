# SPIC Tape Data Management System (Django)

A modern Django-based web application for managing tape library requests at SeisData Processing and Interpretation Centre (SPIC).

## 🚀 Features

### ✅ Authentication System
- **CPF-based Login**: Users authenticate using their CPF number and password
- **Role-based Access**: Regular users and administrators with different permissions
- **Secure Sessions**: Django's built-in session management

### ✅ Dashboard System
- **Personalized Dashboard**: Shows user-specific statistics and quick actions
- **Three Main Options**:
  - **Request**: Submit new tape library requests
  - **Pending**: View and manage submitted requests with file upload capability
  - **Completed**: Review approved and completed requests

### ✅ Request Management
- **Comprehensive Form**: Captures all necessary tape request information
- **Status Tracking**: Pending → Approved → Completed workflow
- **Search & Filter**: Find requests by tape ID, project name, or survey name
- **Pagination**: Efficient handling of large request lists

### ✅ File Upload System
- **Secure Upload**: Support for .txt and .pdf files (max 10MB)
- **File Validation**: Automatic file type and size validation
- **Multiple Files**: Users can upload multiple documents per request

### ✅ Admin Features
- **Full Admin Panel**: Django admin interface for system management
- **Status Management**: Approve, reject, or complete requests
- **User Management**: Create and manage user accounts
- **Bulk Actions**: Process multiple requests simultaneously

## 🛠️ Technology Stack

- **Backend**: Django 4.2.7 (Python)
- **Database**: SQLite (development) / PostgreSQL (production ready)
- **Frontend**: Bootstrap 5.1.3 + Custom CSS
- **Icons**: Font Awesome 6.0
- **File Handling**: Django's built-in file upload system

## 📦 Installation & Setup

### Prerequisites
- Python 3.8+
- pip (Python package manager)

### Installation Steps

1. **Clone/Navigate to the project directory**
   ```bash
   cd /path/to/tapelibrary
   ```

2. **Create and activate virtual environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run database migrations**
   ```bash
   python manage.py migrate
   ```

5. **Create a superuser (admin)**
   ```bash
   python manage.py createsuperuser
   ```

6. **Start the development server**
   ```bash
   python manage.py runserver
   ```

7. **Access the application**
   - Main Application: http://127.0.0.1:8000/
   - Admin Panel: http://127.0.0.1:8000/admin/

## 👥 User Accounts

### Test Accounts Created
- **Admin User**:
  - CPF: `123.456.789-00`
  - Password: `admin123`
  - Role: Administrator (can manage all requests)

- **Regular User**:
  - CPF: `111.222.333-44`
  - Username: `testuser`
  - Password: `testpass123`
  - Role: Regular user (can only see own requests)

## 🎯 Usage Guide

### For Regular Users
1. **Login**: Use your CPF number and password
2. **Dashboard**: View your request statistics
3. **Submit Request**: Click "Request" to submit a new tape request
4. **Track Requests**: Use "Pending" to view and upload files to your requests
5. **View History**: Check "Completed" for approved/finished requests

### For Administrators
1. **Login**: Use admin CPF and password
2. **Manage Requests**: View all user requests in Pending section
3. **Update Status**: Approve, reject, or complete requests
4. **Admin Panel**: Access full system management at `/admin/`
5. **User Management**: Create and manage user accounts

## 📁 Project Structure

```
tapelibrary/
├── manage.py
├── requirements.txt
├── README.md
├── tapelibrary_project/          # Django project settings
│   ├── settings.py
│   ├── urls.py
│   └── ...
├── tapemanagement/               # Main Django app
│   ├── models.py                 # Database models
│   ├── views.py                  # View logic
│   ├── forms.py                  # Form definitions
│   ├── admin.py                  # Admin configuration
│   ├── urls.py                   # URL patterns
│   └── templates/                # HTML templates
├── static/                       # Static files (CSS, JS, images)
├── media/                        # User uploaded files
└── venv/                         # Virtual environment
```

## 🔧 Key Models

### CustomUser
- Extends Django's User model with CPF number field
- Supports CPF-based authentication

### TapeRequest
- Stores all tape request information
- Tracks status (pending/approved/rejected/completed)
- Links to user who submitted the request

### FileUpload
- Manages file uploads associated with requests
- Validates file types and sizes
- Tracks upload metadata

## 🚀 Deployment Notes

For production deployment:
1. Set `DEBUG = False` in settings.py
2. Configure proper database (PostgreSQL recommended)
3. Set up static file serving (WhiteNoise included)
4. Configure email settings for notifications
5. Set up proper domain and SSL certificate

## 🔒 Security Features

- CSRF protection on all forms
- File upload validation
- User permission checks
- SQL injection protection (Django ORM)
- XSS protection (Django templates)

## 📞 Support

For technical support or questions about the system, contact your system administrator.

---

**© 2024 SPIC Tape Data Management System - SeisData Processing and Interpretation Centre**
