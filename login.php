<?php include("components/header.php"); ?>
<?php

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "ongc";

$conn = new mysqli($servername, $username, $password, $dbname);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $cpf = $_POST['cpf'];
    $password = $_POST['password'];

    // Prepared statement to prevent SQL injection
    $stmt = $conn->prepare("SELECT * FROM users WHERE cpf = ? AND password = ?");
    $stmt->bind_param("ss", $cpf, $password);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Correct credentials
        header("Location: request.php");
    } else {
        // Incorrect credentials
        header("Location: index.html?error=true");
    }

    $stmt->close();
}

$conn->close();
?>

