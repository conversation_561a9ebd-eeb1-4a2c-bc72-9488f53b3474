<?php
include("database.php");

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    
    $stmt = $conn->prepare("INSERT INTO tapedetails (cpf_number,username,tapeid,datatype, date, area, survey_name, acquisition_year, group_name,activity,remarks) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $cpf_number = isset($_POST['cpf_number']) ? $_POST['cpf_number'] : (isset($_GET['cpf_number']) ? $_GET['cpf_number'] : '');
    $username = $_POST['username'];
    $tapeid = $_POST['tapeid'];
    $datatype = $_POST['datatype'];
    $date = $_POST['date'];
    $area = $_POST['area'];
    $survey_name = htmlspecialchars(isset($_POST['survey_name']) ? $_POST['survey_name'] : (isset($_GET['survey_name']) ? $_GET['survey_name'] : ''));
    $acquisition_year = htmlspecialchars(isset($_POST['acquisition_year']) ? $_POST['acquisition_year'] : (isset($_GET['acquisition_year']) ? $_GET['acquisition_year'] : ''));
    $group_name = $_POST['group'];
    $activity = $_POST['activity'];
    $remarks = $_POST['remarks'];

    $stmt->bind_param("issssssisss", $cpf_number, $username, $tapeid, $datatype, $date, $area, $survey_name, $acquisition_year, $group_name, $activity, $remarks);

    if ($stmt->execute()) {
        header("Location: tape.php?message=New record created successfully");
        exit();
    } else {
        header("Location: tape.php?message=Error: " . urlencode($stmt->error));
        exit();
    }

    $stmt->close();
    $conn->close();
}
