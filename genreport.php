<?php include("components/header.php"); ?>
<html>
<head>
    <title>Button Page</title>
    <link href="public/css/tailwindLocal.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            background: linear-gradient(135deg, #f0f0f0 25%, #d0d0d0 100%);
            margin: 0;
            padding: 0;
        }
        h1 {
            color: #990000;
            font-family: 'Trebuchet MS', sans-serif;
            text-align: center;
            font-size: 30px;
            font-weight: bold;
            padding: 20px;
            background: #fff;
            margin: 0;
            border-bottom: 2px solid #0366d6;
        }
        p.intro-text {
            color: #0000e6;
            font-family: 'Brush Script MT', cursive;
            font-size: 16px;
            width: 80%;
            margin: 20px auto;
            text-align: center;
        }
        .carousel {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        }
        .carousel-inner {
            position: relative;
            overflow: hidden;
            height: 500px;
            width: 100%;
        }
        .carousel-item img {
            height: 500px;
            width: 100%;
            object-fit: cover;
        }
        .carousel-item {
            display: none;
        }
        .carousel-item.active {
            display: block;
        }
        .carousel-controls {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            display: flex;
            justify-content: space-between;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .carousel:hover .carousel-controls {
            opacity: 1;
        }
        .carousel-control {
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
        }
        .button-container {
            text-align: center;
            margin-top: 20px;
        }
        .button-container a, .button-container button {
            background-color: #0366d6;
            color: white;
            font-weight: bold;
            padding: 14px 32px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            transition: background-color 0.3s ease;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }
        .button-container a:hover, .button-container button:hover {
            background-color: #0056b3;
        }
        .generate-report-container {
            margin-top: 20px;
            text-align: center;
        }
        .generate-report-container form {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            max-width: 600px;
            margin: 0 auto;
        }
        .generate-report-container label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .generate-report-container input {
            width: calc(100% - 20px);
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .generate-report-container button {
            padding: 14px 32px;
            font-size: 16px;
            color: white;
            background-color: #0366d6;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }
        .generate-report-container button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
<div  id="generateReportSection">
        <form action="generate_report.php" method="POST" class="bg-white p-4 rounded shadow-md max-w-md mx-auto">
            <div class="mb-4">
                <label for="startDate" class="block text-sm font-medium text-gray-700">Start Date:</label>
                <input type="date" id="startDate" name="startDate" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            </div>
            <div class="mb-4">
                <label for="endDate" class="block text-sm font-medium text-gray-700">End Date:</label>
                <input type="date" id="endDate" name="endDate" required class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            </div>
            <button type="submit" class="bg-blue-500 text-white font-semibold py-2 px-4 rounded">Submit</button>
        </form>
    </div>
</body>
</html>

