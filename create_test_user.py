#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tapelibrary_project.settings')
django.setup()

from tapemanagement.models import CustomUser

# Create a test user
user, created = CustomUser.objects.get_or_create(
    cpf_number='111.222.333-44',
    defaults={
        'username': 'testuser',
        'email': '<EMAIL>',
        'is_staff': False,
        'is_superuser': False,
    }
)

if created:
    user.set_password('testpass123')
    user.save()
    print(f"Test user created: CPF {user.cpf_number}, Username: {user.username}")
else:
    print(f"Test user already exists: CPF {user.cpf_number}, Username: {user.username}")
